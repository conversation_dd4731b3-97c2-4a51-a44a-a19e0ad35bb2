import React, { Suspense } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { MetricCard, StatusCard } from '~/components/ui/enhanced-card';
import { LoadingOverlay, ProgressBar, ModuleSkeleton } from '~/components/ui/loading-states';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { SuccessFlipWrapper } from '~/components/ui/SuccessFlipWrapper';
import { ThinkingIndicator, IntelligenceProgress, AIStatusBadge } from '../../shared/AIComponents';
import { HoverLift, Pressable, StaggerContainer, StaggerItem } from '../../shared/EnhancedMicrointeractions';
import { cn, typography, layout, animations, states, visualEffects } from '~/lib/ui-utils';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';

// Enhanced components
import {
  EnhancedIcon,
  DashboardIcon,
  MetricsIcon,
  IntelligenceIcon,
  AnalyticsIcon,
  LighthouseIcon
} from '../../shared/EnhancedIconSystem';
import {
  EnhancedTooltip,
  EnhancedProgressBar,
  EnhancedCircularProgress,
  MetricProgressCard
} from '../../shared/EnhancedTooltipAndProgress';
import {
  ResponsiveGrid,
  MetricsGrid,
  Container,
  FlexLayout
} from '../../shared/EnhancedGridLayouts';

import {
  CpuIcon,
  ChartLineIcon,
  FileTextIcon,
  SparklesIcon,
  ClockIcon,
  ActivityIcon,
  HomeIcon,
  ArrowRightIcon,
} from '../../shared/icons';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { QuickActions } from './QuickActions';
import { ActivityFeed } from './ActivityFeed';
import { SmartRecommendations } from './SmartRecommendations';

export function Dashboard() {
  const {
    currentProject,
    projectContext,
    insights,
    activeAgents,
    knowledgeSources,
    learningEvents,
    navigateToModule,
  } = useLighthouseStore();

  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (!currentProject || !projectContext) {
    return (
      <Container size="lg" padding="lg" className="h-full">
        <FlexLayout direction="col" align="center" justify="center" className="h-full">
          <motion.div
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9, y: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.6, ease: "easeOut" }}
          >
            <HoverLift liftHeight={8} scale={1.03} duration={0.3} disabled={prefersReducedMotion}>
              <AnimatedBorderWrapper
                isLoading={false}
                variant="gradient"
                borderColor="#3b82f6"
                className="rounded-xl"
                disabled={prefersReducedMotion}
              >
                <StatusCard
                  status="info"
                  title="Welcome to Lighthouse"
                  description="Create a project to start building contextual intelligence with AI-powered insights and autonomous agents."
                  icon={<DashboardIcon size="lg" variant="primary" animated={!prefersReducedMotion} />}
                  action={
                    <Pressable disabled={prefersReducedMotion}>
                      <Button className={cn(
                        visualEffects.gradients.primary,
                        visualEffects.shadows.colored.primary,
                        'text-primary-foreground',
                        animations.liftOnHover,
                        'transition-all duration-300'
                      )}>
                        <LighthouseIcon size="sm" className="mr-2" animated={!prefersReducedMotion} />
                        Create Your First Project
                      </Button>
                    </Pressable>
                  }
                  className={cn("max-w-lg", visualEffects.cards.elevated)}
                />
              </AnimatedBorderWrapper>
            </HoverLift>
          </motion.div>

          {/* Enhanced AI Status Indicator */}
          <motion.div
            className="mt-8"
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.3 }}
          >
            <FlexLayout align="center" gap="md">
              <AIStatusBadge status="idle" label="Ready to assist" animated={!prefersReducedMotion} />
              <EnhancedTooltip content="Lighthouse AI is ready to help you build intelligent systems">
                <IntelligenceIcon size="md" variant="muted" animated={!prefersReducedMotion} />
              </EnhancedTooltip>
            </FlexLayout>
          </motion.div>
        </FlexLayout>
      </Container>
    );
  }

  // Calculate metrics
  const recentInsights = insights.filter(
    i => i.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;
  
  const learningVelocity = learningEvents.filter(
    e => e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const knowledgeDepth = currentProject.intelligence.domainExpertise.concepts.length;

  return (
    <Container size="xl" padding="lg">
      <FlexLayout direction="col" gap="lg">
        {/* Enhanced Project Overview Metrics */}
        <motion.div
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.5 }}
        >
          <MetricsGrid layout="comfortable" animated={!prefersReducedMotion}>
            {/* Learning Progress Card */}
            <EnhancedTooltip content="AI learning progress and daily velocity">
              <MetricProgressCard
                title="Learning Progress"
                value={currentProject.intelligence.learningLevel}
                max={100}
                change={{
                  value: `+${learningVelocity} insights today`,
                  type: learningVelocity > 0 ? 'increase' : 'neutral'
                }}
                icon={<IntelligenceIcon size="lg" variant="primary" animated={!prefersReducedMotion} />}
                variant="default"
                animated={!prefersReducedMotion}
                className={cn(visualEffects.cards.floating)}
              />
            </EnhancedTooltip>

            {/* Knowledge Depth Card */}
            <EnhancedTooltip content="Domain knowledge concepts and sources">
              <SuccessFlipWrapper
                isSuccess={knowledgeDepth > 10}
                disabled={knowledgeDepth <= 10 || prefersReducedMotion}
              >
                <MetricProgressCard
                  title="Knowledge Depth"
                  value={knowledgeDepth}
                  max={50}
                  change={{
                    value: `${knowledgeSources.length} sources`,
                    type: 'neutral'
                  }}
                  icon={<EnhancedIcon name="book-text" size="lg" variant="success" animated={!prefersReducedMotion} />}
                  variant={knowledgeDepth > 10 ? 'success' : 'default'}
                  animated={!prefersReducedMotion}
                  className={cn(visualEffects.cards.floating)}
                />
              </SuccessFlipWrapper>
            </EnhancedTooltip>

            {/* Active Intelligence Card */}
            <EnhancedTooltip content="Currently running AI agents and processes">
              <AnimatedBorderWrapper
                isLoading={activeAgents.length > 0}
                variant="rotate"
                borderColor="#10b981"
                className="rounded-lg"
                disabled={prefersReducedMotion}
              >
                <MetricProgressCard
                  title="Active Intelligence"
                  value={activeAgents.length}
                  max={10}
                  change={{
                    value: `${projectContext.runningAgents.length} in project`,
                    type: activeAgents.length > 0 ? 'increase' : 'neutral'
                  }}
                  icon={<LighthouseIcon size="lg" variant="success" animated={!prefersReducedMotion} />}
                  variant={activeAgents.length > 0 ? 'success' : 'default'}
                  animated={!prefersReducedMotion}
                  className={cn(visualEffects.cards.floating)}
                />
              </AnimatedBorderWrapper>
            </EnhancedTooltip>

            {/* Recent Insights Card */}
            <EnhancedTooltip content="AI-generated insights from this week">
              <SuccessFlipWrapper
                isSuccess={recentInsights > 0}
                disabled={recentInsights === 0 || prefersReducedMotion}
              >
                <MetricProgressCard
                  title="Recent Insights"
                  value={recentInsights}
                  max={20}
                  change={{
                    value: `${insights.length} total insights`,
                    type: recentInsights > 0 ? 'increase' : 'neutral'
                  }}
                  icon={<AnalyticsIcon size="lg" variant="warning" animated={!prefersReducedMotion} />}
                  variant={recentInsights > 0 ? 'success' : 'default'}
                  animated={!prefersReducedMotion}
                  className={cn(visualEffects.cards.floating)}
                />
              </SuccessFlipWrapper>
            </EnhancedTooltip>
          </MetricsGrid>
        </motion.div>

        {/* Enhanced Main Content Grid */}
        <ResponsiveGrid
          columns={{ mobile: 1, tablet: 1, desktop: 3 }}
          gap="lg"
          animated={!prefersReducedMotion}
        >
          {/* Left Column - Project Status & Quick Actions */}
          <FlexLayout direction="col" gap="lg">
            {/* Enhanced Project Goal Card */}
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.2 }}
            >
              <EnhancedTooltip content="Current project objectives and status">
                <HoverLift liftHeight={6} scale={1.02} duration={0.3} disabled={prefersReducedMotion}>
                  <AnimatedBorderWrapper
                    isLoading={false}
                    variant="pulse"
                    borderColor="#8b5cf6"
                    className="rounded-lg"
                    disabled={prefersReducedMotion}
                  >
                    <Card className={cn(visualEffects.cards.elevated, 'transition-all duration-300')}>
                      <CardHeader>
                        <CardTitle className={cn(typography.h4, 'flex items-center gap-3')}>
                          <MetricsIcon size="lg" variant="primary" animated={!prefersReducedMotion} />
                          Project Goal
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className={cn(typography.body, 'mb-6')}>{currentProject.goal}</p>
                        <FlexLayout direction="col" gap="sm">
                          <FlexLayout justify="between" align="center">
                            <span className="text-muted-foreground text-sm">Status</span>
                            <motion.div
                              initial={prefersReducedMotion ? {} : { scale: 0 }}
                              animate={prefersReducedMotion ? {} : { scale: 1 }}
                              transition={{ duration: 0.3, delay: 0.5 }}
                            >
                              <Badge
                                variant="outline"
                                className={cn(
                                  'capitalize transition-all duration-300',
                                  currentProject.status === 'active' && 'border-green-200 text-green-700 bg-green-50 shadow-sm'
                                )}
                              >
                                {currentProject.status}
                              </Badge>
                            </motion.div>
                          </FlexLayout>
                          <FlexLayout justify="between" align="center">
                            <span className="text-muted-foreground text-sm">Domain</span>
                            <span className={cn(typography.label, 'capitalize')}>{currentProject.domain}</span>
                          </FlexLayout>
                          <FlexLayout justify="between" align="center">
                            <span className="text-muted-foreground text-sm">Created</span>
                            <span className={typography.label}>
                              {new Date(currentProject.created).toLocaleDateString()}
                            </span>
                          </FlexLayout>
                        </FlexLayout>
                      </CardContent>
                    </Card>
                  </AnimatedBorderWrapper>
                </HoverLift>
              </EnhancedTooltip>
            </motion.div>

            {/* Enhanced Quick Actions */}
            <Suspense fallback={<div className="h-32 animate-pulse bg-muted rounded-lg" />}>
              <QuickActions />
            </Suspense>
          </FlexLayout>

          {/* Center Column - Enhanced Activity Feed */}
          <motion.div
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.3 }}
          >
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted rounded-lg" />}>
              <ActivityFeed />
            </Suspense>
          </motion.div>

          {/* Right Column - Enhanced AI Recommendations */}
          <FlexLayout direction="col" gap="lg">
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.4 }}
            >
              <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
                <SmartRecommendations />
              </Suspense>
            </motion.div>

            {/* Enhanced Domain Expertise */}
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.5, delay: prefersReducedMotion ? 0 : 0.5 }}
            >
              <EnhancedTooltip content="AI domain expertise and knowledge areas">
                <HoverLift liftHeight={6} scale={1.02} duration={0.3} disabled={prefersReducedMotion}>
                  <AnimatedBorderWrapper
                    isLoading={false}
                    variant="gradient"
                    borderColor="#f59e0b"
                    className="rounded-lg"
                    disabled={prefersReducedMotion}
                  >
                    <Card className={cn(visualEffects.cards.elevated, 'transition-all duration-300')}>
                      <CardHeader>
                        <CardTitle className={cn(typography.h4, 'flex items-center gap-3')}>
                          <AnalyticsIcon size="lg" variant="warning" animated={!prefersReducedMotion} />
                          Domain Expertise
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <FlexLayout direction="col" gap="md">
                          <div>
                            <FlexLayout justify="between" align="center" className="mb-3">
                              <span className={cn(typography.label)}>
                                {currentProject.intelligence.domainExpertise.primaryDomain}
                              </span>
                              <motion.span
                                className={cn(typography.caption, 'text-primary font-semibold')}
                                initial={prefersReducedMotion ? {} : { opacity: 0 }}
                                animate={prefersReducedMotion ? {} : { opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.6 }}
                              >
                                Level {Math.floor(currentProject.intelligence.domainExpertise.expertiseLevel / 20)}
                              </motion.span>
                            </FlexLayout>
                            <EnhancedProgressBar
                              value={currentProject.intelligence.domainExpertise.expertiseLevel}
                              max={100}
                              variant="warning"
                              animated={!prefersReducedMotion}
                              showPercentage={false}
                              size="md"
                            />
                          </div>

                          {currentProject.intelligence.domainExpertise.relatedDomains.length > 0 && (
                            <motion.div
                              initial={prefersReducedMotion ? {} : { opacity: 0, y: 10 }}
                              animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
                              transition={{ duration: 0.4, delay: 0.7 }}
                            >
                              <p className={cn(typography.caption, 'mb-3')}>Related Domains</p>
                              <FlexLayout wrap gap="sm">
                                {currentProject.intelligence.domainExpertise.relatedDomains.map((domain, index) => (
                                  <motion.div
                                    key={domain}
                                    initial={prefersReducedMotion ? {} : { opacity: 0, scale: 0.8 }}
                                    animate={prefersReducedMotion ? {} : { opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                                    whileHover={prefersReducedMotion ? {} : { scale: 1.05 }}
                                    whileTap={prefersReducedMotion ? {} : { scale: 0.95 }}
                                  >
                                    <Badge
                                      variant="secondary"
                                      className={cn(
                                        'transition-all duration-300 hover:bg-secondary/80 cursor-pointer',
                                        'hover:shadow-md'
                                      )}
                                    >
                                      {domain}
                                    </Badge>
                                  </motion.div>
                                ))}
                              </FlexLayout>
                            </motion.div>
                          )}

                          <motion.div
                            initial={prefersReducedMotion ? {} : { opacity: 0, y: 10 }}
                            animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.9 }}
                          >
                            <Pressable onPress={() => navigateToModule('knowledge')} disabled={prefersReducedMotion}>
                              <Button
                                variant="outline"
                                size="sm"
                                className={cn(
                                  'w-full transition-all duration-300',
                                  visualEffects.gradients.primary,
                                  visualEffects.shadows.colored.primary,
                                  'border-primary/30 hover:border-primary/50',
                                  'text-primary-foreground hover:text-white',
                                  animations.liftOnHover,
                                  states.focusRing
                                )}
                                onClick={() => navigateToModule('knowledge')}
                              >
                                <FlexLayout align="center" gap="sm">
                                  Explore Knowledge Graph
                                  <ArrowRightIcon size={16} />
                                </FlexLayout>
                              </Button>
                            </Pressable>
                          </motion.div>
                        </FlexLayout>
                      </CardContent>
                    </Card>
                  </AnimatedBorderWrapper>
                </HoverLift>
              </EnhancedTooltip>
            </motion.div>
          </FlexLayout>
        </ResponsiveGrid>
      </FlexLayout>
    </Container>
  );
}