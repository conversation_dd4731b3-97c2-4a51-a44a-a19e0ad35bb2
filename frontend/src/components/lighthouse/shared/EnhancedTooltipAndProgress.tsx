import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, animations, visualEffects, accessibility } from '~/lib/ui-utils';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';

// Enhanced Tooltip Component
interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  className?: string;
  disabled?: boolean;
}

export function EnhancedTooltip({
  content,
  children,
  position = 'top',
  delay = 500,
  className,
  disabled = false,
}: TooltipProps) {
  const [isVisible, setIsVisible] = React.useState(false);
  const [timeoutId, setTimeoutId] = React.useState<NodeJS.Timeout | null>(null);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const showTooltip = () => {
    if (disabled) return;
    
    const id = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2',
  };

  const arrowClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-popover',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-popover',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-popover',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-popover',
  };

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className={cn(
              'absolute z-50 pointer-events-none',
              positionClasses[position]
            )}
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
            exit={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
            <div className={cn(
              'px-3 py-2 text-sm bg-popover text-popover-foreground rounded-lg shadow-lg border',
              'max-w-xs whitespace-nowrap',
              visualEffects.shadows.medium,
              className
            )}>
              {content}
            </div>
            
            {/* Tooltip arrow */}
            <div className={cn(
              'absolute w-0 h-0 border-4',
              arrowClasses[position]
            )} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Enhanced Progress Indicator Components
interface ProgressBarProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  animated?: boolean;
  showPercentage?: boolean;
  label?: string;
  className?: string;
}

export function EnhancedProgressBar({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  animated = true,
  showPercentage = true,
  label,
  className,
}: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const variantClasses = {
    default: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
  };

  return (
    <div className={cn('w-full', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && <span className="text-sm font-medium text-foreground">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-muted-foreground">{Math.round(percentage)}%</span>
          )}
        </div>
      )}
      
      <div className={cn(
        'w-full bg-secondary rounded-full overflow-hidden',
        sizeClasses[size]
      )}>
        <motion.div
          className={cn(
            'h-full rounded-full transition-colors duration-300',
            variantClasses[variant]
          )}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={
            prefersReducedMotion 
              ? { duration: 0 }
              : { 
                  duration: animated ? 1 : 0.3, 
                  ease: 'easeOut',
                  delay: animated ? 0.2 : 0
                }
          }
        />
      </div>
    </div>
  );
}

// Circular Progress Indicator
interface CircularProgressProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  variant?: 'default' | 'success' | 'warning' | 'error';
  animated?: boolean;
  showPercentage?: boolean;
  className?: string;
}

export function EnhancedCircularProgress({
  value,
  max = 100,
  size = 80,
  strokeWidth = 8,
  variant = 'default',
  animated = true,
  showPercentage = true,
  className,
}: CircularProgressProps) {
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const variantColors = {
    default: 'stroke-primary',
    success: 'stroke-green-500',
    warning: 'stroke-yellow-500',
    error: 'stroke-red-500',
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-secondary"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          className={variantColors[variant]}
          style={{
            strokeDasharray,
          }}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={
            prefersReducedMotion 
              ? { duration: 0 }
              : { 
                  duration: animated ? 1.5 : 0.3, 
                  ease: 'easeOut',
                  delay: animated ? 0.2 : 0
                }
          }
        />
      </svg>
      
      {/* Percentage text */}
      {showPercentage && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <span className="text-sm font-semibold text-foreground">
            {Math.round(percentage)}%
          </span>
        </motion.div>
      )}
    </div>
  );
}

// Metric Progress Card Component
interface MetricProgressCardProps {
  title: string;
  value: number;
  max?: number;
  change?: {
    value: string;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error';
  animated?: boolean;
  className?: string;
}

export function MetricProgressCard({
  title,
  value,
  max = 100,
  change,
  icon,
  variant = 'default',
  animated = true,
  className,
}: MetricProgressCardProps) {
  const percentage = Math.min((value / max) * 100, 100);

  const changeColors = {
    increase: 'text-green-600',
    decrease: 'text-red-600',
    neutral: 'text-muted-foreground',
  };

  return (
    <motion.div
      className={cn(
        'p-6 rounded-lg border bg-card',
        visualEffects.cards.elevated,
        className
      )}
      initial={animated ? { opacity: 0, y: 20 } : {}}
      animate={animated ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.3 }}
      whileHover={animated ? { y: -2, transition: { duration: 0.2 } } : {}}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {icon && (
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              {icon}
            </div>
          )}
          <div>
            <h3 className="font-semibold text-foreground">{title}</h3>
            {change && (
              <p className={cn('text-sm', changeColors[change.type])}>
                {change.value}
              </p>
            )}
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-2xl font-bold text-foreground">{value}</div>
          <div className="text-sm text-muted-foreground">of {max}</div>
        </div>
      </div>
      
      <EnhancedProgressBar
        value={value}
        max={max}
        variant={variant}
        animated={animated}
        showPercentage={false}
      />
    </motion.div>
  );
}
