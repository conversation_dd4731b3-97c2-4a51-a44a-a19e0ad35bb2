import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, animations, visualEffects, errorStates } from '~/lib/ui-utils';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { EnhancedIcon } from './EnhancedIconSystem';

// Enhanced Loading State Component
interface EnhancedLoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'card' | 'overlay';
  showBorder?: boolean;
  className?: string;
}

export function EnhancedLoadingState({
  message = 'Loading...',
  size = 'md',
  variant = 'default',
  showBorder = true,
  className,
}: EnhancedLoadingStateProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const sizeClasses = {
    sm: 'h-16 w-16',
    md: 'h-24 w-24',
    lg: 'h-32 w-32',
  };

  const LoadingSpinner = () => (
    <motion.div
      className={cn('relative', sizeClasses[size])}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Outer ring with animated border */}
      <AnimatedBorderWrapper
        isLoading={true}
        variant="rotate"
        borderColor="#3b82f6"
        className="rounded-full h-full w-full"
        disabled={prefersReducedMotion || !showBorder}
      >
        <div className="h-full w-full rounded-full bg-background flex items-center justify-center">
          {/* Inner pulsing dot */}
          <motion.div
            className="h-3 w-3 bg-primary rounded-full"
            animate={prefersReducedMotion ? {} : {
              scale: [1, 1.5, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </div>
      </AnimatedBorderWrapper>
    </motion.div>
  );

  if (variant === 'overlay') {
    return (
      <motion.div
        className={cn(
          'fixed inset-0 z-50 flex items-center justify-center',
          'bg-background/80 backdrop-blur-sm',
          className
        )}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <div className="text-center space-y-4">
          <LoadingSpinner />
          {message && (
            <motion.p
              className="text-sm text-muted-foreground"
              initial={prefersReducedMotion ? {} : { opacity: 0, y: 10 }}
              animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              {message}
            </motion.p>
          )}
        </div>
      </motion.div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className={cn('p-6', className)}>
        <CardContent className="flex flex-col items-center justify-center space-y-4">
          <LoadingSpinner />
          {message && (
            <p className="text-sm text-muted-foreground text-center">{message}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
      <LoadingSpinner />
      {message && (
        <p className="text-sm text-muted-foreground text-center">{message}</p>
      )}
    </div>
  );
}

// Enhanced Error State Component
interface EnhancedErrorStateProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  severity?: 'error' | 'warning' | 'critical';
  className?: string;
}

export function EnhancedErrorState({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  action,
  severity = 'error',
  className,
}: EnhancedErrorStateProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const severityConfig = {
    error: {
      borderColor: '#ef4444',
      iconVariant: 'error' as const,
      bgClass: errorStates.background,
      textClass: errorStates.text,
    },
    warning: {
      borderColor: '#f59e0b',
      iconVariant: 'warning' as const,
      bgClass: errorStates.warning.background,
      textClass: errorStates.warning.text,
    },
    critical: {
      borderColor: '#dc2626',
      iconVariant: 'error' as const,
      bgClass: errorStates.critical.background,
      textClass: errorStates.critical.text,
    },
  };

  const config = severityConfig[severity];

  return (
    <motion.div
      className={className}
      initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.95 }}
      animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1 }}
      transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
    >
      <AnimatedBorderWrapper
        isLoading={false}
        variant="pulse"
        borderColor={config.borderColor}
        className="rounded-lg"
        disabled={prefersReducedMotion}
      >
        <Card className={cn(config.bgClass, 'border-2', errorStates.border)}>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <motion.div
                initial={prefersReducedMotion ? {} : { scale: 0 }}
                animate={prefersReducedMotion ? {} : { scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <EnhancedIcon
                  name="activity"
                  size="lg"
                  variant={config.iconVariant}
                  animated={!prefersReducedMotion}
                  state="error"
                />
              </motion.div>
              <span className={config.textClass}>{title}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className={cn('text-sm', config.textClass)}>{message}</p>
            {action && (
              <motion.div
                initial={prefersReducedMotion ? {} : { opacity: 0, y: 10 }}
                animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <Button
                  onClick={action.onClick}
                  variant="outline"
                  className={cn(
                    errorStates.button,
                    'transition-all duration-300'
                  )}
                >
                  {action.label}
                </Button>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </AnimatedBorderWrapper>
    </motion.div>
  );
}

// Enhanced Success State Component with 3D Flip
interface EnhancedSuccessStateProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  showFlip?: boolean;
  className?: string;
}

export function EnhancedSuccessState({
  title = 'Success!',
  message = 'Operation completed successfully.',
  action,
  showFlip = true,
  className,
}: EnhancedSuccessStateProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  return (
    <motion.div
      className={className}
      initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, rotateY: showFlip ? -90 : 0, scale: 0.8 }}
      animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, rotateY: 0, scale: 1 }}
      transition={{
        duration: prefersReducedMotion ? 0.1 : 0.6,
        ease: 'easeOut',
        rotateY: { duration: 0.8, ease: 'backOut' },
      }}
      style={{ transformStyle: 'preserve-3d' }}
    >
      <Card className={cn(
        'border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800',
        visualEffects.shadows.colored.success
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <motion.div
              initial={prefersReducedMotion ? {} : { scale: 0, rotate: -180 }}
              animate={prefersReducedMotion ? {} : { scale: 1, rotate: 0 }}
              transition={{ 
                duration: 0.5, 
                delay: 0.3,
                type: 'spring',
                stiffness: 200,
                damping: 15
              }}
            >
              <EnhancedIcon
                name="activity"
                size="lg"
                variant="success"
                animated={!prefersReducedMotion}
                state="success"
              />
            </motion.div>
            <span className="text-green-700 dark:text-green-300">{title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-green-600 dark:text-green-400">{message}</p>
          {action && (
            <motion.div
              initial={prefersReducedMotion ? {} : { opacity: 0, y: 10 }}
              animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <Button
                onClick={action.onClick}
                className={cn(
                  'bg-green-600 hover:bg-green-700 text-white',
                  'transition-all duration-300',
                  animations.liftOnHover
                )}
              >
                {action.label}
              </Button>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

// State Wrapper Component
interface StateWrapperProps {
  loading?: boolean;
  error?: string | null;
  success?: boolean;
  children: React.ReactNode;
  loadingMessage?: string;
  errorAction?: {
    label: string;
    onClick: () => void;
  };
  successMessage?: string;
  className?: string;
}

export function StateWrapper({
  loading = false,
  error = null,
  success = false,
  children,
  loadingMessage,
  errorAction,
  successMessage,
  className,
}: StateWrapperProps) {
  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {loading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <EnhancedLoadingState message={loadingMessage} variant="card" />
          </motion.div>
        )}
        
        {error && !loading && (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <EnhancedErrorState
              message={error}
              action={errorAction}
            />
          </motion.div>
        )}
        
        {success && !loading && !error && (
          <motion.div
            key="success"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <EnhancedSuccessState
              message={successMessage}
            />
          </motion.div>
        )}
        
        {!loading && !error && !success && (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
