import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn, visualEffects } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';

// Enhanced components
import { 
  EnhancedIcon,
  DashboardIcon,
  KnowledgeIcon,
  ResearchIcon,
  IntelligenceIcon,
  ChatIcon,
  SourcesIcon,
  AnalyticsIcon,
  LighthouseIcon
} from '../shared/EnhancedIconSystem';

import { 
  EnhancedTooltip, 
  EnhancedProgressBar, 
  EnhancedCircularProgress,
  MetricProgressCard 
} from '../shared/EnhancedTooltipAndProgress';

import { 
  ResponsiveGrid, 
  MetricsGrid, 
  Container,
  FlexLayout 
} from '../shared/EnhancedGridLayouts';

import { 
  EnhancedLoadingState, 
  EnhancedErrorState, 
  EnhancedSuccessState,
  StateWrapper 
} from '../shared/EnhancedStateComponents';

export function EnhancedUIDemo() {
  const [demoState, setDemoState] = useState<'loading' | 'error' | 'success' | 'content'>('content');
  const [progress, setProgress] = useState(65);

  const iconNames = [
    'sparkles', 'home', 'activity', 'cpu', 'chart-line', 
    'file-text', 'message-circle', 'book-text', 'telescope'
  ] as const;

  const iconVariants = ['default', 'primary', 'secondary', 'success', 'warning', 'error'] as const;

  return (
    <Container size="xl" padding="lg">
      <FlexLayout direction="col" gap="lg">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className={visualEffects.cards.elevated}>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <LighthouseIcon size="xl" variant="primary" animated />
                Enhanced Lighthouse UI Components Demo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Showcase of modern, accessible, and responsive UI components with animations 
                that respect user preferences.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Icons Section */}
        <Card className={visualEffects.cards.elevated}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <EnhancedIcon name="sparkles" size="lg" variant="primary" animated />
              Enhanced Icons
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FlexLayout direction="col" gap="lg">
              {/* Icon Sizes */}
              <div>
                <h4 className="font-semibold mb-3">Sizes</h4>
                <FlexLayout align="center" gap="md">
                  {(['xs', 'sm', 'md', 'lg', 'xl', '2xl'] as const).map(size => (
                    <EnhancedTooltip key={size} content={`Size: ${size}`}>
                      <DashboardIcon size={size} variant="primary" animated />
                    </EnhancedTooltip>
                  ))}
                </FlexLayout>
              </div>

              {/* Icon Variants */}
              <div>
                <h4 className="font-semibold mb-3">Variants</h4>
                <FlexLayout align="center" gap="md" wrap>
                  {iconVariants.map(variant => (
                    <EnhancedTooltip key={variant} content={`Variant: ${variant}`}>
                      <div className="text-center">
                        <IntelligenceIcon size="lg" variant={variant} animated />
                        <p className="text-xs mt-1 capitalize">{variant}</p>
                      </div>
                    </EnhancedTooltip>
                  ))}
                </FlexLayout>
              </div>

              {/* Icon States */}
              <div>
                <h4 className="font-semibold mb-3">States</h4>
                <FlexLayout align="center" gap="md">
                  <EnhancedTooltip content="Idle state">
                    <AnalyticsIcon size="lg" state="idle" animated />
                  </EnhancedTooltip>
                  <EnhancedTooltip content="Loading state">
                    <AnalyticsIcon size="lg" state="loading" animated />
                  </EnhancedTooltip>
                  <EnhancedTooltip content="Active state">
                    <AnalyticsIcon size="lg" state="active" animated />
                  </EnhancedTooltip>
                  <EnhancedTooltip content="Success state">
                    <AnalyticsIcon size="lg" state="success" animated />
                  </EnhancedTooltip>
                  <EnhancedTooltip content="Error state">
                    <AnalyticsIcon size="lg" state="error" animated />
                  </EnhancedTooltip>
                </FlexLayout>
              </div>
            </FlexLayout>
          </CardContent>
        </Card>

        {/* Progress Indicators Section */}
        <Card className={visualEffects.cards.elevated}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <EnhancedIcon name="activity" size="lg" variant="success" animated />
              Progress Indicators
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FlexLayout direction="col" gap="lg">
              {/* Progress Bars */}
              <div>
                <h4 className="font-semibold mb-3">Progress Bars</h4>
                <FlexLayout direction="col" gap="md">
                  <EnhancedProgressBar
                    value={progress}
                    label="Default Progress"
                    variant="default"
                    animated
                  />
                  <EnhancedProgressBar
                    value={85}
                    label="Success Progress"
                    variant="success"
                    animated
                  />
                  <EnhancedProgressBar
                    value={45}
                    label="Warning Progress"
                    variant="warning"
                    animated
                  />
                  <EnhancedProgressBar
                    value={25}
                    label="Error Progress"
                    variant="error"
                    animated
                  />
                </FlexLayout>
                
                <div className="mt-4">
                  <Button 
                    onClick={() => setProgress(Math.random() * 100)}
                    variant="outline"
                    size="sm"
                  >
                    Randomize Progress
                  </Button>
                </div>
              </div>

              {/* Circular Progress */}
              <div>
                <h4 className="font-semibold mb-3">Circular Progress</h4>
                <FlexLayout align="center" gap="lg">
                  <EnhancedCircularProgress value={75} variant="default" animated />
                  <EnhancedCircularProgress value={90} variant="success" animated />
                  <EnhancedCircularProgress value={60} variant="warning" animated />
                  <EnhancedCircularProgress value={30} variant="error" animated />
                </FlexLayout>
              </div>
            </FlexLayout>
          </CardContent>
        </Card>

        {/* Metric Cards Section */}
        <Card className={visualEffects.cards.elevated}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <EnhancedIcon name="chart-line" size="lg" variant="warning" animated />
              Metric Cards
            </CardTitle>
          </CardHeader>
          <CardContent>
            <MetricsGrid layout="comfortable" animated>
              <MetricProgressCard
                title="Learning Progress"
                value={78}
                max={100}
                change={{ value: '+12 this week', type: 'increase' }}
                icon={<IntelligenceIcon size="lg" variant="primary" animated />}
                variant="default"
                animated
              />
              <MetricProgressCard
                title="Knowledge Depth"
                value={45}
                max={50}
                change={{ value: '15 sources', type: 'neutral' }}
                icon={<KnowledgeIcon size="lg" variant="success" animated />}
                variant="success"
                animated
              />
              <MetricProgressCard
                title="Active Agents"
                value={3}
                max={10}
                change={{ value: '2 running', type: 'increase' }}
                icon={<LighthouseIcon size="lg" variant="warning" animated />}
                variant="warning"
                animated
              />
              <MetricProgressCard
                title="Recent Insights"
                value={8}
                max={20}
                change={{ value: '24 total', type: 'neutral' }}
                icon={<AnalyticsIcon size="lg" variant="error" animated />}
                variant="error"
                animated
              />
            </MetricsGrid>
          </CardContent>
        </Card>

        {/* State Components Section */}
        <Card className={visualEffects.cards.elevated}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <EnhancedIcon name="activity" size="lg" variant="secondary" animated />
              State Components
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FlexLayout direction="col" gap="lg">
              {/* State Controls */}
              <div>
                <h4 className="font-semibold mb-3">Demo States</h4>
                <FlexLayout gap="sm" wrap>
                  <Button 
                    onClick={() => setDemoState('content')}
                    variant={demoState === 'content' ? 'default' : 'outline'}
                    size="sm"
                  >
                    Content
                  </Button>
                  <Button 
                    onClick={() => setDemoState('loading')}
                    variant={demoState === 'loading' ? 'default' : 'outline'}
                    size="sm"
                  >
                    Loading
                  </Button>
                  <Button 
                    onClick={() => setDemoState('error')}
                    variant={demoState === 'error' ? 'default' : 'outline'}
                    size="sm"
                  >
                    Error
                  </Button>
                  <Button 
                    onClick={() => setDemoState('success')}
                    variant={demoState === 'success' ? 'default' : 'outline'}
                    size="sm"
                  >
                    Success
                  </Button>
                </FlexLayout>
              </div>

              {/* State Demo */}
              <div className="min-h-[200px]">
                <StateWrapper
                  loading={demoState === 'loading'}
                  error={demoState === 'error' ? 'This is a demo error message' : null}
                  success={demoState === 'success'}
                  loadingMessage="Loading demo content..."
                  successMessage="Demo operation completed successfully!"
                  errorAction={{
                    label: 'Try Again',
                    onClick: () => setDemoState('content')
                  }}
                >
                  <div className="p-8 text-center">
                    <h3 className="text-lg font-semibold mb-2">Demo Content</h3>
                    <p className="text-muted-foreground">
                      This is the normal content state. Use the buttons above to see different states.
                    </p>
                  </div>
                </StateWrapper>
              </div>
            </FlexLayout>
          </CardContent>
        </Card>

        {/* Responsive Grid Demo */}
        <Card className={visualEffects.cards.elevated}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <EnhancedIcon name="book-text" size="lg" variant="muted" animated />
              Responsive Grids
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveGrid
              columns={{ mobile: 1, tablet: 2, desktop: 3, wide: 4 }}
              gap="md"
              animated
            >
              {Array.from({ length: 8 }, (_, i) => (
                <Card key={i} className="p-4">
                  <FlexLayout align="center" gap="sm">
                    <EnhancedIcon 
                      name={iconNames[i % iconNames.length]} 
                      size="md" 
                      variant="primary" 
                      animated 
                    />
                    <div>
                      <h4 className="font-medium">Grid Item {i + 1}</h4>
                      <p className="text-sm text-muted-foreground">
                        Responsive grid item
                      </p>
                    </div>
                  </FlexLayout>
                </Card>
              ))}
            </ResponsiveGrid>
          </CardContent>
        </Card>
      </FlexLayout>
    </Container>
  );
}
