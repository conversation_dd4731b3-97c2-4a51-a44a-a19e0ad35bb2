import React, { useState, useRef, useEffect } from "react";
import { <PERSON>, useRouter } from "@tanstack/react-router";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "~/utils/auth-context";
import {
  Lightbulb,
  GraduationCap,
  Trophy,
  Building2,
  ChevronDown,
  Menu,
  X,
  User,
  LogOut,
  BarChart3,
  FileText,
  Users,
  Settings,
  BookOpen,
  Award,
  TrendingUp,
  <PERSON>rkles,
  <PERSON>lette,
  Zap,
  Shield
} from "lucide-react";

interface NavbarProps {
  leftItems?: React.ReactNode;
  centerItems?: React.ReactNode;
  rightItems?: React.ReactNode;
  className?: string;
}

interface NavItemProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
  icon?: React.ReactNode;
  isActive?: boolean;
  className?: string;
}

interface DropdownMenuProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  align?: "left" | "right" | "center";
}

interface EnhancedNavItemProps {
  href: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  isProminent?: boolean;
  badge?: string;
  className?: string;
}

// Enhanced nav link component with icons and active state
export const NavItem: React.FC<NavItemProps> = ({ href, children, onClick, icon, isActive, className = "" }) => {
  return (
    <Link
      to={href}
      className={`
        flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg
        ${isActive 
          ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
          : 'text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
        }
        ${className}
      `}
      onClick={onClick}
    >
      {icon && <span className="w-4 h-4">{icon}</span>}
      {children}
    </Link>
  );
};

// Enhanced prominent nav item (for important sections like Lighthouse)
export const ProminentNavItem: React.FC<EnhancedNavItemProps> = ({ 
  href, 
  children, 
  icon, 
  isProminent = false,
  badge,
  className = "" 
}) => {
  return (
    <Link
      to={href}
      className={`
        relative flex items-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-300 rounded-lg
        ${isProminent 
          ? 'text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105' 
          : 'text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
        }
        ${className}
      `}
    >
      {icon && <span className="w-4 h-4">{icon}</span>}
      {children}
      {badge && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
          {badge}
        </span>
      )}
      {isProminent && (
        <motion.div
          className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/20 to-purple-400/20"
          animate={{
            opacity: [0, 0.5, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </Link>
  );
};

// Enhanced dropdown menu with glass morphism
export const DropdownMenu: React.FC<DropdownMenuProps> = ({ 
  trigger, 
  children, 
  align = "center" 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  const alignmentClasses = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 transform -translate-x-1/2"
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="cursor-pointer"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {trigger}
      </div>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={`
              absolute top-full mt-2 z-50 min-w-[240px]
              bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg 
              border border-gray-200/20 dark:border-gray-700/20 
              rounded-xl shadow-xl 
              ring-1 ring-black/5 dark:ring-white/5
              ${alignmentClasses[align]}
            `}
          >
            <div className="p-2">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Enhanced mobile menu button
const MobileMenuButton: React.FC<{ isOpen: boolean; onClick: () => void }> = ({ 
  isOpen, 
  onClick 
}) => {
  return (
    <motion.button
      onClick={onClick}
      className="md:hidden p-2 rounded-lg text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
      aria-label="Toggle mobile menu"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <AnimatePresence mode="wait">
        {isOpen ? (
          <motion.div
            key="close"
            initial={{ rotate: -90, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            exit={{ rotate: 90, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <X className="w-6 h-6" />
          </motion.div>
        ) : (
          <motion.div
            key="menu"
            initial={{ rotate: 90, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            exit={{ rotate: -90, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Menu className="w-6 h-6" />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.button>
  );
};

// Main navbar component
export const SimpleNavbar: React.FC<NavbarProps> = ({
  leftItems,
  centerItems,
  rightItems,
  className = ""
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navClasses = `
    sticky top-0 z-50 w-full transition-all duration-300
    ${isScrolled 
      ? "bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/20 dark:border-gray-700/20" 
      : "bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm"
    }
    ${className}
  `;

  return (
    <nav className={navClasses}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left section */}
          <div className="flex items-center space-x-4">
            {leftItems}
          </div>

          {/* Center section - hidden on mobile */}
          <div className="hidden md:flex items-center space-x-4">
            {centerItems}
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-4">
              {rightItems}
            </div>
            
            <MobileMenuButton 
              isOpen={isMobileMenuOpen}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            />
          </div>
        </div>

        {/* Mobile menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="md:hidden border-t border-gray-200 dark:border-gray-700"
            >
              <div className="py-4 space-y-2">
                {centerItems}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  {rightItems}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );
};

// Enhanced menu items component with better navigation structure
export const DefaultMenuItems: React.FC = () => {
  const router = useRouter();
  const currentPath = router.state.location.pathname;

  return (
    <div className="flex items-center space-x-1">
      {/* Lighthouse - Prominent Feature */}
      <ProminentNavItem 
        href="/lighthouse" 
        icon={<Lightbulb />}
        isProminent={true}
        className="mr-2"
      >
        Lighthouse
      </ProminentNavItem>

      {/* EGA Academy Dropdown */}
      <DropdownMenu
        trigger={
          <motion.div 
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GraduationCap className="w-4 h-4" />
            Academy
            <ChevronDown className="w-3 h-3 ml-1" />
          </motion.div>
        }
      >
        <div className="space-y-1">
          <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Learning Hub
          </div>
          <NavItem 
            href="/homepage/ega-academy" 
            icon={<BookOpen />}
            isActive={currentPath === '/homepage/ega-academy'}
          >
            EGA Academy
          </NavItem>
          <NavItem 
            href="/training-needs-analysis" 
            icon={<TrendingUp />}
            isActive={currentPath === '/training-needs-analysis'}
          >
            Training Needs Analysis
          </NavItem>
        </div>
      </DropdownMenu>

      {/* Achievements */}
      <DropdownMenu
        trigger={
          <motion.div 
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Trophy className="w-4 h-4" />
            Achievements
            <ChevronDown className="w-3 h-3 ml-1" />
          </motion.div>
        }
      >
        <div className="space-y-1">
          <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Recognition
          </div>
          <NavItem 
            href="/wins-of-week" 
            icon={<Award />}
            isActive={currentPath === '/wins-of-week'}
          >
            Wins of the Week
          </NavItem>
          <NavItem 
            href="/team-dashboard" 
            icon={<Users />}
            isActive={currentPath === '/team-dashboard'}
          >
            Team Dashboard
          </NavItem>
          <NavItem 
            href="/wins-of-week/achievement-list" 
            icon={<BarChart3 />}
            isActive={currentPath === '/wins-of-week/achievement-list'}
          >
            Achievement Analytics
          </NavItem>
        </div>
      </DropdownMenu>
      
      {/* Vendor Management */}
      <DropdownMenu
        trigger={
          <motion.div 
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Building2 className="w-4 h-4" />
            Vendor Management
            <ChevronDown className="w-3 h-3 ml-1" />
          </motion.div>
        }
      >
        <div className="space-y-1">
          <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Business Operations
          </div>
          <NavItem 
            href="/vendor-management" 
            icon={<Building2 />}
            isActive={currentPath === '/vendor-management'}
          >
            Vendor Directory
          </NavItem>
          <NavItem 
            href="/vendor-management/communications" 
            icon={<FileText />}
            isActive={currentPath.includes('/communications')}
          >
            Communications
          </NavItem>
          <NavItem 
            href="/vendor-management/projects" 
            icon={<Settings />}
            isActive={currentPath.includes('/projects')}
          >
            Project Tracking
          </NavItem>
          <NavItem 
            href="/vendor-management/analytics" 
            icon={<BarChart3 />}
            isActive={currentPath.includes('/analytics')}
          >
            Analytics Dashboard
          </NavItem>
        </div>
      </DropdownMenu>

      {/* Demos */}
      <DropdownMenu
        trigger={
          <motion.div
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Sparkles className="w-4 h-4" />
            Demos
            <ChevronDown className="w-3 h-3 ml-1" />
          </motion.div>
        }
      >
        <div className="space-y-1">
          <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Interactive Showcases
          </div>
          <NavItem
            href="/demos"
            icon={<Sparkles />}
            isActive={currentPath === '/demos'}
          >
            All Demos
          </NavItem>
          <NavItem
            href="/animation-demo"
            icon={<Zap />}
            isActive={currentPath === '/animation-demo'}
          >
            Animation Demo
          </NavItem>
          <NavItem
            href="/auth-demo"
            icon={<Shield />}
            isActive={currentPath === '/auth-demo'}
          >
            Auth Demo
          </NavItem>
          <NavItem
            href="/icons-demo"
            icon={<Palette />}
            isActive={currentPath === '/icons-demo'}
          >
            Icons Demo
          </NavItem>
        </div>
      </DropdownMenu>
    </div>
  );
};

// Enhanced profile menu
export const ProfileMenu: React.FC<{ setActive?: (item: string | null) => void; active?: string | null }> = () => {
  const { user } = useAuth();
  
  if (!user) return null;

  return (
    <DropdownMenu
      trigger={
        <motion.div 
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-all duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <motion.div 
            className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold shadow-md"
            whileHover={{ scale: 1.1 }}
          >
            {user.first_name?.[0] || user.email?.[0] || 'U'}
          </motion.div>
          <span className="hidden md:block">{user.first_name || 'User'}</span>
          <ChevronDown className="w-3 h-3 ml-1 hidden md:block" />
        </motion.div>
      }
      align="right"
    >
      <div className="space-y-1">
        {/* User Info Header */}
        <div className="px-3 py-3 border-b border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
              {user.first_name?.[0] || user.email?.[0] || 'U'}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {user.first_name || 'User'}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[180px]">
                {user.email}
              </div>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="py-1">
          <NavItem 
            href="/roles" 
            icon={<User />}
            className="mx-1"
          >
            Profile & Settings
          </NavItem>
          
          {/* Logout Button */}
          <motion.button
            className="w-full flex items-center gap-2 mx-1 px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200 rounded-lg mt-2"
            onClick={() => {
              // Handle logout
              window.location.href = '/auth';
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <LogOut className="w-4 h-4" />
            Sign Out
          </motion.button>
        </div>
      </div>
    </DropdownMenu>
  );
};

// Export everything
export default SimpleNavbar;