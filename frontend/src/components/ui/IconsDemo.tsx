import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Button } from './button';
import { Badge } from './badge';
import { Input } from './input';
import { Icons } from './icons';
import { Search, Copy, Check } from 'lucide-react';

interface IconItem {
  name: string;
  component: React.ComponentType<{ className?: string }>;
  category: string;
}

const iconCategories = {
  navigation: 'Navigation',
  actions: 'Actions',
  communication: 'Communication',
  media: 'Media',
  system: 'System',
  business: 'Business',
};

const iconList: IconItem[] = [
  // Navigation
  { name: 'search', component: Icons.search, category: 'navigation' },
  { name: 'home', component: Icons.home, category: 'navigation' },
  { name: 'menu', component: Icons.menu, category: 'navigation' },
  { name: 'chevronDown', component: Icons.chevronDown, category: 'navigation' },
  { name: 'chevronLeft', component: Icons.chevronLeft, category: 'navigation' },
  { name: 'chevronRight', component: Icons.chevronRight, category: 'navigation' },
  { name: 'chevronUp', component: Icons.chevronUp, category: 'navigation' },
  { name: 'arrowLeft', component: Icons.arrowLeft, category: 'navigation' },
  { name: 'arrowRight', component: Icons.arrowRight, category: 'navigation' },

  // Actions
  { name: 'check', component: Icons.check, category: 'actions' },
  { name: 'edit', component: Icons.edit, category: 'actions' },
  { name: 'delete', component: Icons.delete, category: 'actions' },
  { name: 'save', component: Icons.save, category: 'actions' },
  { name: 'copy', component: Icons.copy, category: 'actions' },
  { name: 'refresh', component: Icons.refresh, category: 'actions' },
  { name: 'download', component: Icons.download, category: 'actions' },
  { name: 'upload', component: Icons.upload, category: 'actions' },
  { name: 'share', component: Icons.share, category: 'actions' },
  { name: 'heart', component: Icons.heart, category: 'actions' },
  { name: 'star', component: Icons.star, category: 'actions' },
  { name: 'add', component: Icons.add, category: 'actions' },
  { name: 'close', component: Icons.close, category: 'actions' },

  // Communication
  { name: 'bell', component: Icons.bell, category: 'communication' },
  { name: 'mail', component: Icons.mail, category: 'communication' },
  { name: 'phone', component: Icons.phone, category: 'communication' },
  { name: 'externalLink', component: Icons.externalLink, category: 'communication' },

  // System
  { name: 'settings', component: Icons.settings, category: 'system' },
  { name: 'user', component: Icons.user, category: 'system' },
  { name: 'calendar', component: Icons.calendar, category: 'system' },
  { name: 'folder', component: Icons.folder, category: 'system' },
  { name: 'file', component: Icons.file, category: 'system' },
  { name: 'fileText', component: Icons.fileText, category: 'system' },
  { name: 'lock', component: Icons.lock, category: 'system' },
  { name: 'unlock', component: Icons.unlock, category: 'system' },
  { name: 'shield', component: Icons.shield, category: 'system' },
  { name: 'globe', component: Icons.globe, category: 'system' },
  { name: 'wifi', component: Icons.wifi, category: 'system' },
  { name: 'battery', component: Icons.battery, category: 'system' },

  // Media
  { name: 'image', component: Icons.image, category: 'media' },
  { name: 'video', component: Icons.video, category: 'media' },
  { name: 'music', component: Icons.music, category: 'media' },

  // Business
  { name: 'barChart', component: Icons.barChart, category: 'business' },
  { name: 'pieChart', component: Icons.pieChart, category: 'business' },
  { name: 'activity', component: Icons.activity, category: 'business' },
  { name: 'trendingUp', component: Icons.trendingUp, category: 'business' },
  { name: 'trendingDown', component: Icons.trendingDown, category: 'business' },
  { name: 'target', component: Icons.target, category: 'business' },
  { name: 'brain', component: Icons.brain, category: 'business' },
  { name: 'rocket', component: Icons.rocket, category: 'business' },
];

export function IconsDemo() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [copiedIcon, setCopiedIcon] = useState<string | null>(null);

  const filteredIcons = iconList.filter(icon => {
    const matchesSearch = icon.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || icon.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCopyIcon = (iconName: string) => {
    const iconCode = `<Icons.${iconName} className="h-4 w-4" />`;
    navigator.clipboard.writeText(iconCode);
    setCopiedIcon(iconName);
    setTimeout(() => setCopiedIcon(null), 2000);
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Custom Icons Library
          </h1>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Explore our custom icon collection. Click any icon to copy its usage code.
          </p>
        </motion.div>

        {/* Controls */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search & Filter Icons</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search icons..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('all')}
                >
                  All ({iconList.length})
                </Button>
                {Object.entries(iconCategories).map(([key, label]) => {
                  const count = iconList.filter(icon => icon.category === key).length;
                  return (
                    <Button
                      key={key}
                      variant={selectedCategory === key ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(key)}
                    >
                      {label} ({count})
                    </Button>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Icons Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Icons ({filteredIcons.length})</span>
                <Badge variant="outline">
                  Click to copy code
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredIcons.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    No icons found matching your search criteria.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-4">
                  {filteredIcons.map((icon, index) => {
                    const IconComponent = icon.component;
                    const isCopied = copiedIcon === icon.name;
                    
                    return (
                      <motion.div
                        key={icon.name}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: index * 0.02 }}
                        className="group"
                      >
                        <button
                          onClick={() => handleCopyIcon(icon.name)}
                          className="w-full p-4 border rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 flex flex-col items-center gap-2 relative"
                        >
                          <div className="relative">
                            <IconComponent className="h-6 w-6 text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
                            {isCopied && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute -top-1 -right-1 bg-green-500 text-white rounded-full p-1"
                              >
                                <Check className="h-3 w-3" />
                              </motion.div>
                            )}
                          </div>
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-center">
                            {icon.name}
                          </span>
                          <Badge 
                            variant="secondary" 
                            className="text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            {iconCategories[icon.category as keyof typeof iconCategories]}
                          </Badge>
                        </button>
                      </motion.div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Usage Guide */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Import Icons</h4>
                <code className="block bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm">
                  import &#123; Icons &#125; from '~/components/ui/icons'
                </code>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Use Icons</h4>
                <code className="block bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm">
                  &lt;Icons.search className="h-4 w-4" /&gt;
                </code>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Available Sizes</h4>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Icons.star className="h-3 w-3" />
                    <code className="text-sm">h-3 w-3</code>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icons.star className="h-4 w-4" />
                    <code className="text-sm">h-4 w-4</code>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icons.star className="h-5 w-5" />
                    <code className="text-sm">h-5 w-5</code>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icons.star className="h-6 w-6" />
                    <code className="text-sm">h-6 w-6</code>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icons.star className="h-8 w-8" />
                    <code className="text-sm">h-8 w-8</code>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
