import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { motion } from 'framer-motion';

// Mock framer-motion for testing
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: vi.fn(),
    stop: vi.fn(),
  }),
}));

// Mock AccessibilityUtils
vi.mock('@/utils/accessibilityUtils', () => ({
  AccessibilityUtils: {
    prefersReducedMotion: vi.fn(() => false),
  },
}));

// Import components to test
import { EnhancedIcon } from '~/components/lighthouse/shared/EnhancedIconSystem';
import { EnhancedTooltip, EnhancedProgressBar, MetricProgressCard } from '~/components/lighthouse/shared/EnhancedTooltipAndProgress';
import { ResponsiveGrid, MetricsGrid } from '~/components/lighthouse/shared/EnhancedGridLayouts';
import { EnhancedLoadingState, EnhancedErrorState, EnhancedSuccessState } from '~/components/lighthouse/shared/EnhancedStateComponents';

describe('Enhanced Lighthouse UI Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('EnhancedIcon', () => {
    it('renders with correct size and variant', () => {
      render(
        <EnhancedIcon 
          name="sparkles" 
          size="lg" 
          variant="primary" 
          aria-label="Test icon"
        />
      );
      
      const icon = screen.getByLabelText('Test icon');
      expect(icon).toBeInTheDocument();
    });

    it('handles click events', () => {
      const handleClick = vi.fn();
      render(
        <EnhancedIcon 
          name="home" 
          onClick={handleClick}
          aria-label="Clickable icon"
        />
      );
      
      const icon = screen.getByLabelText('Clickable icon');
      fireEvent.click(icon);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('respects disabled state', () => {
      const handleClick = vi.fn();
      render(
        <EnhancedIcon 
          name="home" 
          onClick={handleClick}
          disabled={true}
          aria-label="Disabled icon"
        />
      );
      
      const icon = screen.getByLabelText('Disabled icon');
      expect(icon).toHaveClass('opacity-50');
      expect(icon).toHaveClass('cursor-not-allowed');
    });

    it('shows tooltip when provided', async () => {
      render(
        <EnhancedIcon 
          name="sparkles" 
          tooltip="This is a tooltip"
          aria-label="Icon with tooltip"
        />
      );
      
      const icon = screen.getByLabelText('Icon with tooltip');
      fireEvent.mouseEnter(icon);
      
      await waitFor(() => {
        expect(screen.getByText('This is a tooltip')).toBeInTheDocument();
      });
    });
  });

  describe('EnhancedTooltip', () => {
    it('shows tooltip on hover', async () => {
      render(
        <EnhancedTooltip content="Tooltip content">
          <button>Hover me</button>
        </EnhancedTooltip>
      );
      
      const button = screen.getByText('Hover me');
      fireEvent.mouseEnter(button);
      
      await waitFor(() => {
        expect(screen.getByText('Tooltip content')).toBeInTheDocument();
      });
    });

    it('hides tooltip on mouse leave', async () => {
      render(
        <EnhancedTooltip content="Tooltip content">
          <button>Hover me</button>
        </EnhancedTooltip>
      );
      
      const button = screen.getByText('Hover me');
      fireEvent.mouseEnter(button);
      
      await waitFor(() => {
        expect(screen.getByText('Tooltip content')).toBeInTheDocument();
      });
      
      fireEvent.mouseLeave(button);
      
      await waitFor(() => {
        expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument();
      });
    });

    it('respects disabled state', () => {
      render(
        <EnhancedTooltip content="Tooltip content" disabled={true}>
          <button>Hover me</button>
        </EnhancedTooltip>
      );
      
      const button = screen.getByText('Hover me');
      fireEvent.mouseEnter(button);
      
      // Tooltip should not appear when disabled
      expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument();
    });
  });

  describe('EnhancedProgressBar', () => {
    it('renders with correct value and percentage', () => {
      render(
        <EnhancedProgressBar 
          value={75} 
          max={100} 
          label="Progress" 
          showPercentage={true}
        />
      );
      
      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('handles different variants', () => {
      const { rerender } = render(
        <EnhancedProgressBar value={50} variant="success" />
      );
      
      // Check if success variant is applied
      const progressBar = document.querySelector('.bg-green-500');
      expect(progressBar).toBeInTheDocument();
      
      rerender(<EnhancedProgressBar value={50} variant="error" />);
      
      // Check if error variant is applied
      const errorProgressBar = document.querySelector('.bg-red-500');
      expect(errorProgressBar).toBeInTheDocument();
    });
  });

  describe('MetricProgressCard', () => {
    it('renders metric information correctly', () => {
      render(
        <MetricProgressCard
          title="Test Metric"
          value={80}
          max={100}
          change={{ value: '+5 today', type: 'increase' }}
          icon={<div data-testid="test-icon">Icon</div>}
        />
      );
      
      expect(screen.getByText('Test Metric')).toBeInTheDocument();
      expect(screen.getByText('80')).toBeInTheDocument();
      expect(screen.getByText('of 100')).toBeInTheDocument();
      expect(screen.getByText('+5 today')).toBeInTheDocument();
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    });

    it('applies correct change color based on type', () => {
      const { rerender } = render(
        <MetricProgressCard
          title="Test"
          value={50}
          change={{ value: '+10', type: 'increase' }}
        />
      );
      
      expect(screen.getByText('+10')).toHaveClass('text-green-600');
      
      rerender(
        <MetricProgressCard
          title="Test"
          value={50}
          change={{ value: '-5', type: 'decrease' }}
        />
      );
      
      expect(screen.getByText('-5')).toHaveClass('text-red-600');
    });
  });

  describe('ResponsiveGrid', () => {
    it('renders children in grid layout', () => {
      render(
        <ResponsiveGrid>
          <div>Item 1</div>
          <div>Item 2</div>
          <div>Item 3</div>
        </ResponsiveGrid>
      );
      
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.getByText('Item 3')).toBeInTheDocument();
    });

    it('applies correct gap classes', () => {
      const { container } = render(
        <ResponsiveGrid gap="lg">
          <div>Item</div>
        </ResponsiveGrid>
      );
      
      const grid = container.firstChild;
      expect(grid).toHaveClass('gap-8');
    });
  });

  describe('EnhancedLoadingState', () => {
    it('renders loading message', () => {
      render(<EnhancedLoadingState message="Loading data..." />);
      expect(screen.getByText('Loading data...')).toBeInTheDocument();
    });

    it('renders as overlay variant', () => {
      render(<EnhancedLoadingState variant="overlay" message="Loading..." />);
      
      const overlay = screen.getByText('Loading...').closest('div');
      expect(overlay).toHaveClass('fixed', 'inset-0', 'z-50');
    });

    it('renders as card variant', () => {
      render(<EnhancedLoadingState variant="card" message="Loading..." />);
      
      // Should be wrapped in a card
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('EnhancedErrorState', () => {
    it('renders error message and title', () => {
      render(
        <EnhancedErrorState
          title="Error occurred"
          message="Something went wrong"
        />
      );
      
      expect(screen.getByText('Error occurred')).toBeInTheDocument();
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('renders action button when provided', () => {
      const handleAction = vi.fn();
      render(
        <EnhancedErrorState
          title="Error"
          message="Error message"
          action={{ label: 'Retry', onClick: handleAction }}
        />
      );
      
      const button = screen.getByText('Retry');
      expect(button).toBeInTheDocument();
      
      fireEvent.click(button);
      expect(handleAction).toHaveBeenCalledTimes(1);
    });

    it('applies correct severity styling', () => {
      const { rerender } = render(
        <EnhancedErrorState severity="critical" title="Critical Error" />
      );
      
      // Should have critical error styling
      expect(screen.getByText('Critical Error')).toBeInTheDocument();
      
      rerender(<EnhancedErrorState severity="warning" title="Warning" />);
      expect(screen.getByText('Warning')).toBeInTheDocument();
    });
  });

  describe('EnhancedSuccessState', () => {
    it('renders success message', () => {
      render(
        <EnhancedSuccessState
          title="Success!"
          message="Operation completed"
        />
      );
      
      expect(screen.getByText('Success!')).toBeInTheDocument();
      expect(screen.getByText('Operation completed')).toBeInTheDocument();
    });

    it('renders action button when provided', () => {
      const handleAction = vi.fn();
      render(
        <EnhancedSuccessState
          title="Success"
          action={{ label: 'Continue', onClick: handleAction }}
        />
      );
      
      const button = screen.getByText('Continue');
      expect(button).toBeInTheDocument();
      
      fireEvent.click(button);
      expect(handleAction).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('respects prefers-reduced-motion', () => {
      const { AccessibilityUtils } = require('@/utils/accessibilityUtils');
      AccessibilityUtils.prefersReducedMotion.mockReturnValue(true);
      
      render(<EnhancedIcon name="sparkles" animated={true} />);
      
      // When reduced motion is preferred, animations should be disabled
      expect(AccessibilityUtils.prefersReducedMotion).toHaveBeenCalled();
    });

    it('provides proper ARIA labels', () => {
      render(
        <EnhancedIcon 
          name="home" 
          aria-label="Navigate to home"
        />
      );
      
      const icon = screen.getByLabelText('Navigate to home');
      expect(icon).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      const handleClick = vi.fn();
      render(
        <EnhancedIcon 
          name="home" 
          onClick={handleClick}
          aria-label="Clickable icon"
        />
      );
      
      const icon = screen.getByLabelText('Clickable icon');
      
      // Test Enter key
      fireEvent.keyDown(icon, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      // Test Space key
      fireEvent.keyDown(icon, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });
  });
});
