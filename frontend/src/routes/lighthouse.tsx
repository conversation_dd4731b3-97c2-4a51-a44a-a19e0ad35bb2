import { createFileRoute } from '@tanstack/react-router'
import { LighthouseMain } from '~/components/lighthouse'
import { motion, AnimatePresence } from 'framer-motion'
import { z } from 'zod'
import { cn, visualEffects, animations } from '~/lib/ui-utils'
import { AccessibilityUtils } from '@/utils/accessibilityUtils'

const lighthouseSearchSchema = z.object({
  module: z.enum(['dashboard', 'knowledge', 'research', 'agents', 'chat', 'sources', 'analytics', 'insights']).optional(),
})

export const Route = createFileRoute('/lighthouse')({
  component: LighthouseComponent,
  validateSearch: lighthouseSearchSchema,
})

function LighthouseComponent() {
  const { module } = Route.useSearch()
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion()

  return (
    <div className="h-screen overflow-hidden relative">
      {/* Enhanced Background with Multiple Layers */}
      <div className="absolute inset-0">
        {/* Base gradient background */}
        <div className={cn(
          "absolute inset-0",
          "bg-gradient-to-br from-slate-50 via-blue-50/20 to-purple-50/30",
          "dark:from-gray-950 dark:via-blue-950/20 dark:to-purple-950/30"
        )} />

        {/* Animated mesh gradient overlay */}
        <motion.div
          className={cn(
            "absolute inset-0",
            "bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5",
            "dark:from-blue-400/10 dark:via-purple-400/10 dark:to-pink-400/10"
          )}
          animate={prefersReducedMotion ? {} : {
            background: [
              "linear-gradient(45deg, rgba(59,130,246,0.05), rgba(147,51,234,0.05), rgba(236,72,153,0.05))",
              "linear-gradient(90deg, rgba(147,51,234,0.05), rgba(236,72,153,0.05), rgba(59,130,246,0.05))",
              "linear-gradient(135deg, rgba(236,72,153,0.05), rgba(59,130,246,0.05), rgba(147,51,234,0.05))",
              "linear-gradient(45deg, rgba(59,130,246,0.05), rgba(147,51,234,0.05), rgba(236,72,153,0.05))"
            ]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Enhanced geometric pattern */}
        <div className="absolute inset-0 opacity-[0.015] dark:opacity-[0.03] pointer-events-none">
          <motion.div
            className={cn(
              "absolute inset-0",
              "bg-[linear-gradient(45deg,transparent_35%,rgba(59,130,246,0.1)_35%,rgba(59,130,246,0.1)_65%,transparent_65%),linear-gradient(-45deg,transparent_35%,rgba(147,51,234,0.1)_35%,rgba(147,51,234,0.1)_65%,transparent_65%)]",
              "bg-[length:80px_80px]"
            )}
            animate={prefersReducedMotion ? {} : {
              backgroundPosition: ["0px 0px", "80px 80px"]
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>

        {/* Floating orbs with enhanced animations */}
        <AnimatePresence>
          {!prefersReducedMotion && (
            <>
              <motion.div
                className="absolute w-96 h-96 rounded-full pointer-events-none"
                style={{
                  background: "radial-gradient(circle, rgba(59,130,246,0.15) 0%, rgba(59,130,246,0.05) 50%, transparent 100%)",
                  filter: "blur(40px)"
                }}
                initial={{ x: "25%", y: "25%", scale: 0.8, opacity: 0 }}
                animate={{
                  x: ["25%", "30%", "20%", "25%"],
                  y: ["25%", "20%", "30%", "25%"],
                  scale: [0.8, 1.2, 0.9, 0.8],
                  opacity: [0, 0.7, 0.5, 0.7]
                }}
                transition={{
                  duration: 15,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <motion.div
                className="absolute w-80 h-80 rounded-full pointer-events-none"
                style={{
                  background: "radial-gradient(circle, rgba(147,51,234,0.15) 0%, rgba(147,51,234,0.05) 50%, transparent 100%)",
                  filter: "blur(35px)"
                }}
                initial={{ x: "70%", y: "70%", scale: 0.6, opacity: 0 }}
                animate={{
                  x: ["70%", "65%", "75%", "70%"],
                  y: ["70%", "75%", "65%", "70%"],
                  scale: [0.6, 1, 0.8, 0.6],
                  opacity: [0, 0.6, 0.4, 0.6]
                }}
                transition={{
                  duration: 18,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2
                }}
              />

              <motion.div
                className="absolute w-64 h-64 rounded-full pointer-events-none"
                style={{
                  background: "radial-gradient(circle, rgba(236,72,153,0.12) 0%, rgba(236,72,153,0.04) 50%, transparent 100%)",
                  filter: "blur(30px)"
                }}
                initial={{ x: "10%", y: "80%", scale: 0.7, opacity: 0 }}
                animate={{
                  x: ["10%", "15%", "5%", "10%"],
                  y: ["80%", "75%", "85%", "80%"],
                  scale: [0.7, 1.1, 0.9, 0.7],
                  opacity: [0, 0.5, 0.3, 0.5]
                }}
                transition={{
                  duration: 12,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 4
                }}
              />
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Main Lighthouse Interface with Enhanced Animations */}
      <motion.div
        initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.95, y: 20 }}
        animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
        transition={{
          duration: prefersReducedMotion ? 0.1 : 1,
          ease: "easeOut",
          delay: prefersReducedMotion ? 0 : 0.2
        }}
        className="relative z-10 h-full"
      >
        <LighthouseMain
          initialModule={module || "dashboard"}
          className="h-full"
        />
      </motion.div>
    </div>
  )
}