import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { 
  Sparkles, 
  Shield, 
  Palette, 
  Zap, 
  GraduationCap, 
  Building2, 
  Lightbulb,
  Layers,
  ExternalLink
} from 'lucide-react';

// Import demo components
import { AnimationDemo } from '~/components/ui/AnimationDemo';
import AuthDemo from '~/components/ui/ProfileCard/AuthDemo';
import FeaturesShowcase from '~/components/ui/ProfileCard/FeaturesShowcase';
import { IconsDemo } from '~/components/ui/IconsDemo';
import { EnhancedUIDemo } from '~/components/lighthouse/demo/EnhancedUIDemo';
import { TrainingNeedsAnalysisDemo } from './TrainingNeedsAnalysisDemo';
import { VendorManagementDemo } from './VendorManagementDemo';
import { MantineShowcase } from '~/components/examples/mantine-showcase';

interface DemoTab {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  component: React.ComponentType;
  badges: string[];
  featured?: boolean;
}

const demoTabs: DemoTab[] = [
  {
    id: 'animations',
    label: 'Animation Demo',
    description: 'Interactive UI animations with 3D flips, animated borders, and loading states',
    icon: <Zap className="w-5 h-5" />,
    component: AnimationDemo,
    badges: ['Framer Motion', '3D Effects', 'Accessibility'],
    featured: true,
  },
  {
    id: 'auth',
    label: 'Auth Demo',
    description: 'Beautiful authentication interface with ProfileCard component',
    icon: <Shield className="w-5 h-5" />,
    component: AuthDemo,
    badges: ['Authentication', 'Forms', 'Validation'],
  },
  {
    id: 'profilecard',
    label: 'ProfileCard Features',
    description: 'Comprehensive showcase of ProfileCard component features',
    icon: <Palette className="w-5 h-5" />,
    component: FeaturesShowcase,
    badges: ['UI Components', 'Responsive', 'Interactive'],
  },
  {
    id: 'icons',
    label: 'Icons Library',
    description: 'Custom icons collection with search and copy functionality',
    icon: <Sparkles className="w-5 h-5" />,
    component: IconsDemo,
    badges: ['Icons', 'Custom', 'Interactive'],
  },
  {
    id: 'lighthouse',
    label: 'Lighthouse UI',
    description: 'Enhanced UI components with modern design patterns',
    icon: <Lightbulb className="w-5 h-5" />,
    component: EnhancedUIDemo,
    badges: ['Modern UI', 'Responsive Grid', 'Icons'],
    featured: true,
  },
  {
    id: 'training',
    label: 'Training Analysis',
    description: 'Multi-step wizard for training needs analysis',
    icon: <GraduationCap className="w-5 h-5" />,
    component: TrainingNeedsAnalysisDemo,
    badges: ['Multi-step', 'Forms', 'Workflow'],
  },
  {
    id: 'vendor',
    label: 'Vendor Management',
    description: 'Complete vendor management system with analytics',
    icon: <Building2 className="w-5 h-5" />,
    component: VendorManagementDemo,
    badges: ['Dashboard', 'Analytics', 'CRUD'],
    featured: true,
  },
  {
    id: 'mantine',
    label: 'Mantine Showcase',
    description: 'Comprehensive showcase of Mantine UI components',
    icon: <Layers className="w-5 h-5" />,
    component: MantineShowcase,
    badges: ['Mantine UI', 'Components', 'Interactive'],
  },
];

export function DemosPage() {
  const [activeTab, setActiveTab] = useState('animations');
  const [isLoading, setIsLoading] = useState(false);

  const handleTabChange = (value: string) => {
    setIsLoading(true);
    setActiveTab(value);
    // Simulate loading for smooth transition
    setTimeout(() => setIsLoading(false), 300);
  };

  const ActiveComponent = demoTabs.find(tab => tab.id === activeTab)?.component || AnimationDemo;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <div className="flex justify-center mb-4">
            <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white">
              <Sparkles className="w-8 h-8" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            W-O-W Interactive Demos
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Explore our comprehensive collection of interactive components, demos, and features. 
            Each demo showcases different aspects of our modern UI library and application features.
          </p>
        </motion.div>

        {/* Demo Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 mb-8 h-auto p-1">
              {demoTabs.map((tab) => (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center gap-2 p-4 h-auto relative"
                >
                  {tab.featured && (
                    <Badge 
                      variant="secondary" 
                      className="absolute -top-1 -right-1 text-xs px-1 py-0 bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                    >
                      ✨
                    </Badge>
                  )}
                  {tab.icon}
                  <span className="text-xs font-medium text-center leading-tight">
                    {tab.label}
                  </span>
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Demo Content */}
            <div className="space-y-6">
              {/* Demo Info Card */}
              <Card className="border-2 border-dashed border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    {demoTabs.find(tab => tab.id === activeTab)?.icon}
                    {demoTabs.find(tab => tab.id === activeTab)?.label}
                    {demoTabs.find(tab => tab.id === activeTab)?.featured && (
                      <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                        Featured
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {demoTabs.find(tab => tab.id === activeTab)?.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {demoTabs.find(tab => tab.id === activeTab)?.badges.map((badge) => (
                      <Badge key={badge} variant="outline">
                        {badge}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Demo Component */}
              {demoTabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="relative"
                  >
                    {isLoading && (
                      <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                      </div>
                    )}
                    <Card className="overflow-hidden">
                      <CardContent className="p-0">
                        <tab.component />
                      </CardContent>
                    </Card>
                  </motion.div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-700"
        >
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            Built with React, TypeScript, Tailwind CSS, and modern UI libraries
          </p>
        </motion.div>
      </div>
    </div>
  );
}
