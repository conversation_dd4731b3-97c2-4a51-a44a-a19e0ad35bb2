import SwiftUI
import Foundation

// MARK: - Search Result Models

struct SearchResult: Identifiable, Hashable {
    let id = UUID()
    let title: String
    let subtitle: String?
    let category: SearchCategory
    let action: SearchAction
    let icon: String
    let relevanceScore: Double
    let metadata: [String: String]
    
    var displaySubtitle: String {
        if let subtitle = subtitle, !subtitle.isEmpty {
            return subtitle
        }
        return category.displayName
    }
}

enum SearchCategory: String, CaseIterable {
    case services = "Services"
    case docker = "Docker"
    case logs = "Logs"
    case notifications = "Notifications"
    case systemMetrics = "System Metrics"
    case navigation = "Navigation"
    case actions = "Actions"
    case settings = "Settings"
    
    var displayName: String {
        return rawValue
    }
    
    var icon: String {
        switch self {
        case .services: return "gear"
        case .docker: return "cube"
        case .logs: return "doc.text"
        case .notifications: return "bell"
        case .systemMetrics: return "chart.bar"
        case .navigation: return "location"
        case .actions: return "bolt"
        case .settings: return "gearshape"
        }
    }
    
    var color: Color {
        switch self {
        case .services: return .blue
        case .docker: return .purple
        case .logs: return .orange
        case .notifications: return .red
        case .systemMetrics: return .green
        case .navigation: return .indigo
        case .actions: return .yellow
        case .settings: return .gray
        }
    }
}

enum SearchAction {
    case navigateToService(String)
    case navigateToContainer(String)
    case navigateToView(NavigationDestination)
    case executeAction(QuickAction)
    case openSettings(SettingsTab)
    case showLogs(String?)
    case showNotification(String)
    case showMetric(String)
    case custom(String, () -> Void)
}

enum QuickAction: String, CaseIterable {
    case refreshData = "Refresh Data"
    case startAllServices = "Start All Services"
    case stopAllServices = "Stop All Services"
    case restartAllServices = "Restart All Services"
    case exportLogs = "Export Logs"
    case clearNotifications = "Clear Notifications"
    case takeScreenshot = "Take Screenshot"
    case toggleMenuBar = "Toggle Menu Bar"
    
    var icon: String {
        switch self {
        case .refreshData: return "arrow.clockwise"
        case .startAllServices: return "play.fill"
        case .stopAllServices: return "stop.fill"
        case .restartAllServices: return "restart"
        case .exportLogs: return "square.and.arrow.up"
        case .clearNotifications: return "trash"
        case .takeScreenshot: return "camera"
        case .toggleMenuBar: return "menubar.rectangle"
        }
    }
}

enum SettingsTab: String, CaseIterable {
    case general = "General"
    case connection = "Connection"
    case notifications = "Notifications"
    case appearance = "Appearance"
    case shortcuts = "Shortcuts"
    case advanced = "Advanced"
    case about = "About"
}

// MARK: - Quick Search View

struct QuickSearchView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var appState: AppState
    @StateObject private var searchManager = QuickSearchManager()
    
    @State private var searchText = ""
    @State private var selectedIndex = 0
    @State private var isVisible = false
    
    private var filteredResults: [SearchResult] {
        searchManager.search(query: searchText, appState: appState)
    }
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    dismiss()
                }
            
            // Search interface
            VStack(spacing: 0) {
                searchBar
                
                if !searchText.isEmpty {
                    resultsView
                } else {
                    recentAndSuggestionsView
                }
            }
            .background(VisualEffectView(material: .hudWindow, blendingMode: .behindWindow))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
            .frame(width: 600, height: min(500, CGFloat(max(filteredResults.count, 8)) * 50 + 100))
            .scaleEffect(isVisible ? 1 : 0.8)
            .opacity(isVisible ? 1 : 0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                isVisible = true
            }
        }
        .onKeyPress(.escape) {
            dismiss()
            return .handled
        }
        .onKeyPress(.upArrow) {
            selectedIndex = max(0, selectedIndex - 1)
            return .handled
        }
        .onKeyPress(.downArrow) {
            selectedIndex = min(filteredResults.count - 1, selectedIndex + 1)
            return .handled
        }
        .onKeyPress(.return) {
            executeSelectedResult()
            return .handled
        }
    }
    
    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.title2)
            
            TextField("Search services, containers, actions...", text: $searchText)
                .textFieldStyle(.plain)
                .font(.title3)
                .onSubmit {
                    executeSelectedResult()
                }
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
            
            Text("⌘K")
                .font(.caption)
                .foregroundColor(.tertiary)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(Color.secondary.opacity(0.1))
                .cornerRadius(4)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
    }
    
    private var resultsView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(Array(filteredResults.enumerated()), id: \.element.id) { index, result in
                        SearchResultRow(
                            result: result,
                            isSelected: index == selectedIndex
                        )
                        .onTapGesture {
                            selectedIndex = index
                            executeSelectedResult()
                        }
                        .id(index)
                    }
                }
            }
            .onChange(of: selectedIndex) { _, newIndex in
                withAnimation(.easeInOut(duration: 0.2)) {
                    proxy.scrollTo(newIndex, anchor: .center)
                }
            }
        }
        .frame(maxHeight: 400)
    }
    
    private var recentAndSuggestionsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Quick actions
            VStack(alignment: .leading, spacing: 8) {
                Text("Quick Actions")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                    ForEach(QuickAction.allCases.prefix(6), id: \.self) { action in
                        QuickActionCard(action: action) {
                            executeQuickAction(action)
                        }
                    }
                }
                .padding(.horizontal)
            }
            
            Divider()
            
            // Recent searches or suggestions
            VStack(alignment: .leading, spacing: 8) {
                Text("Suggestions")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                
                LazyVStack(spacing: 0) {
                    ForEach(searchManager.getSuggestions(appState: appState).prefix(4), id: \.id) { result in
                        SearchResultRow(result: result, isSelected: false)
                            .onTapGesture {
                                executeAction(result.action)
                            }
                    }
                }
            }
        }
        .padding(.vertical)
    }
    
    private func executeSelectedResult() {
        guard !filteredResults.isEmpty, selectedIndex < filteredResults.count else { return }
        let result = filteredResults[selectedIndex]
        executeAction(result.action)
    }
    
    private func executeAction(_ action: SearchAction) {
        dismiss()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            switch action {
            case .navigateToService(let serviceName):
                // Navigate to service detail
                NotificationCenter.default.post(
                    name: .navigateToService,
                    object: serviceName
                )
                
            case .navigateToContainer(let containerName):
                // Navigate to container detail
                NotificationCenter.default.post(
                    name: .navigateToContainer,
                    object: containerName
                )
                
            case .navigateToView(let destination):
                NotificationCenter.default.post(
                    name: .navigateToView,
                    object: destination
                )
                
            case .executeAction(let quickAction):
                executeQuickAction(quickAction)
                
            case .openSettings(let tab):
                NotificationCenter.default.post(
                    name: .openSettings,
                    object: tab
                )
                
            case .showLogs(let filter):
                NotificationCenter.default.post(
                    name: .showLogs,
                    object: filter
                )
                
            case .showNotification(let notificationId):
                NotificationCenter.default.post(
                    name: .showNotification,
                    object: notificationId
                )
                
            case .showMetric(let metricName):
                NotificationCenter.default.post(
                    name: .showMetric,
                    object: metricName
                )
                
            case .custom(_, let action):
                action()
            }
        }
    }
    
    private func executeQuickAction(_ action: QuickAction) {
        Task {
            switch action {
            case .refreshData:
                await appState.refreshAllData()
            case .startAllServices:
                await appState.startAllServices()
            case .stopAllServices:
                await appState.stopAllServices()
            case .restartAllServices:
                await appState.restartAllServices()
            case .exportLogs:
                NotificationCenter.default.post(name: .exportLogs, object: nil)
            case .clearNotifications:
                appState.clearAllNotifications()
            case .takeScreenshot:
                NotificationCenter.default.post(name: .takeScreenshot, object: nil)
            case .toggleMenuBar:
                NotificationCenter.default.post(name: .toggleMenuBar, object: nil)
            }
        }
    }
}

// MARK: - Search Result Row

struct SearchResultRow: View {
    let result: SearchResult
    let isSelected: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Icon
            ZStack {
                Circle()
                    .fill(result.category.color.opacity(0.1))
                    .frame(width: 32, height: 32)
                
                Image(systemName: result.icon)
                    .foregroundColor(result.category.color)
                    .font(.system(size: 14, weight: .medium))
            }
            
            // Content
            VStack(alignment: .leading, spacing: 2) {
                Text(result.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(result.displaySubtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // Category badge
            Text(result.category.displayName)
                .font(.caption2)
                .foregroundColor(result.category.color)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(result.category.color.opacity(0.1))
                .cornerRadius(4)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(
            Rectangle()
                .fill(isSelected ? Color.accentColor.opacity(0.1) : Color.clear)
        )
        .contentShape(Rectangle())
    }
}

// MARK: - Quick Action Card

struct QuickActionCard: View {
    let action: QuickAction
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                Image(systemName: action.icon)
                    .foregroundColor(.accentColor)
                    .font(.system(size: 14, weight: .medium))
                
                Text(action.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(6)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Visual Effect View

struct VisualEffectView: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode
    
    func makeNSView(context: Context) -> NSVisualEffectView {
        let view = NSVisualEffectView()
        view.material = material
        view.blendingMode = blendingMode
        view.state = .active
        return view
    }
    
    func updateNSView(_ nsView: NSVisualEffectView, context: Context) {
        nsView.material = material
        nsView.blendingMode = blendingMode
    }
}

// MARK: - Quick Search Manager

@MainActor
class QuickSearchManager: ObservableObject {
    private let searchHistory: [String] = []
    
    func search(query: String, appState: AppState) -> [SearchResult] {
        guard !query.isEmpty else { return [] }
        
        var results: [SearchResult] = []
        let lowercaseQuery = query.lowercased()
        
        // Search services
        for service in appState.services {
            let relevance = calculateRelevance(query: lowercaseQuery, text: service.name.lowercased())
            if relevance > 0 {
                results.append(SearchResult(
                    title: service.name,
                    subtitle: "Status: \(service.status.rawValue.capitalized)",
                    category: .services,
                    action: .navigateToService(service.name),
                    icon: "gear",
                    relevanceScore: relevance,
                    metadata: ["status": service.status.rawValue]
                ))
            }
        }
        
        // Search Docker containers
        for container in appState.dockerContainers {
            let relevance = calculateRelevance(query: lowercaseQuery, text: container.name.lowercased())
            if relevance > 0 {
                results.append(SearchResult(
                    title: container.name,
                    subtitle: "\(container.image) - \(container.state.capitalized)",
                    category: .docker,
                    action: .navigateToContainer(container.name),
                    icon: "cube",
                    relevanceScore: relevance,
                    metadata: ["state": container.state, "image": container.image]
                ))
            }
        }
        
        // Search navigation items
        let navigationItems: [(String, NavigationDestination, String)] = [
            ("Dashboard", .dashboard, "Overview of all systems"),
            ("Services", .services, "Manage backend services"),
            ("Docker", .docker, "Docker containers and images"),
            ("System Metrics", .systemMetrics, "CPU, memory, and disk usage"),
            ("Logs", .logs, "Application and system logs"),
            ("Notifications", .notifications, "System notifications and alerts")
        ]
        
        for (title, destination, description) in navigationItems {
            let relevance = calculateRelevance(query: lowercaseQuery, text: title.lowercased())
            if relevance > 0 {
                results.append(SearchResult(
                    title: title,
                    subtitle: description,
                    category: .navigation,
                    action: .navigateToView(destination),
                    icon: "location",
                    relevanceScore: relevance,
                    metadata: [:]
                ))
            }
        }
        
        // Search quick actions
        for action in QuickAction.allCases {
            let relevance = calculateRelevance(query: lowercaseQuery, text: action.rawValue.lowercased())
            if relevance > 0 {
                results.append(SearchResult(
                    title: action.rawValue,
                    subtitle: nil,
                    category: .actions,
                    action: .executeAction(action),
                    icon: action.icon,
                    relevanceScore: relevance,
                    metadata: [:]
                ))
            }
        }
        
        // Search settings
        for tab in SettingsTab.allCases {
            let relevance = calculateRelevance(query: lowercaseQuery, text: tab.rawValue.lowercased())
            if relevance > 0 {
                results.append(SearchResult(
                    title: "\(tab.rawValue) Settings",
                    subtitle: "Open \(tab.rawValue.lowercased()) settings",
                    category: .settings,
                    action: .openSettings(tab),
                    icon: "gearshape",
                    relevanceScore: relevance,
                    metadata: [:]
                ))
            }
        }
        
        // Search notifications
        for notification in appState.notifications {
            let titleRelevance = calculateRelevance(query: lowercaseQuery, text: notification.title.lowercased())
            let messageRelevance = calculateRelevance(query: lowercaseQuery, text: notification.message.lowercased())
            let relevance = max(titleRelevance, messageRelevance)
            
            if relevance > 0 {
                results.append(SearchResult(
                    title: notification.title,
                    subtitle: notification.message,
                    category: .notifications,
                    action: .showNotification(notification.id.uuidString),
                    icon: "bell",
                    relevanceScore: relevance,
                    metadata: ["category": notification.category.rawValue]
                ))
            }
        }
        
        // Sort by relevance score
        return results.sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    func getSuggestions(appState: AppState) -> [SearchResult] {
        var suggestions: [SearchResult] = []
        
        // Suggest problematic services
        let problemServices = appState.services.filter { $0.status == .error || $0.status == .stopped }
        for service in problemServices.prefix(2) {
            suggestions.append(SearchResult(
                title: service.name,
                subtitle: "Service needs attention - \(service.status.rawValue.capitalized)",
                category: .services,
                action: .navigateToService(service.name),
                icon: "exclamationmark.triangle",
                relevanceScore: 1.0,
                metadata: ["status": service.status.rawValue]
            ))
        }
        
        // Suggest high resource usage
        if let metrics = appState.systemMetrics {
            if metrics.cpu.usage > 80 {
                suggestions.append(SearchResult(
                    title: "High CPU Usage",
                    subtitle: "\(String(format: "%.1f", metrics.cpu.usage))% CPU usage detected",
                    category: .systemMetrics,
                    action: .showMetric("cpu"),
                    icon: "speedometer",
                    relevanceScore: 1.0,
                    metadata: ["value": String(metrics.cpu.usage)]
                ))
            }
            
            if metrics.memory.usedPercentage > 85 {
                suggestions.append(SearchResult(
                    title: "High Memory Usage",
                    subtitle: "\(String(format: "%.1f", (Double(metrics.memory.used) / Double(metrics.memory.total)) * 100))% memory usage detected",
                    category: .systemMetrics,
                    action: .showMetric("memory"),
                    icon: "memorychip",
                    relevanceScore: 1.0,
                    metadata: ["value": String(metrics.memory.usedPercentage)]
                ))
            }
        }
        
        // Suggest recent notifications
        let recentNotifications = appState.notifications
            .filter { !$0.isRead }
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(2)
        
        for notification in recentNotifications {
            suggestions.append(SearchResult(
                title: notification.message,
                subtitle: "Unread notification",
                category: .notifications,
                action: .showNotification(notification.id.uuidString),
                icon: "bell.badge",
                relevanceScore: 1.0,
                metadata: ["type": notification.type.color]
            ))
        }
        
        return suggestions
    }
    
    private func calculateRelevance(query: String, text: String) -> Double {
        guard !query.isEmpty && !text.isEmpty else { return 0 }
        
        // Exact match
        if text == query {
            return 1.0
        }
        
        // Starts with query
        if text.hasPrefix(query) {
            return 0.9
        }
        
        // Contains query
        if text.contains(query) {
            return 0.7
        }
        
        // Fuzzy matching (simple implementation)
        let queryChars = Array(query)
        let textChars = Array(text)
        var queryIndex = 0
        var matches = 0
        
        for char in textChars {
            if queryIndex < queryChars.count && char == queryChars[queryIndex] {
                matches += 1
                queryIndex += 1
            }
        }
        
        if matches == queryChars.count {
            return 0.5 * (Double(matches) / Double(textChars.count))
        }
        
        return 0
    }
}

// MARK: - Additional Notification Names

extension Notification.Name {
    static let navigateToService = Notification.Name("navigateToService")
    static let navigateToContainer = Notification.Name("navigateToContainer")
    static let showLogs = Notification.Name("showLogs")
    static let showNotification = Notification.Name("showNotification")
    static let showMetric = Notification.Name("showMetric")
}

#Preview {
    QuickSearchView()
        .environmentObject(AppState.shared)
}