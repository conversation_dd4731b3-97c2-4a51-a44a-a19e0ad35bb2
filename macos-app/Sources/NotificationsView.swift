import SwiftUI
import UserNotifications

struct NotificationsView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedFilter: NotificationFilter = .all
    @State private var searchText = ""
    @State private var showingSettings = false
    @State private var selectedNotifications = Set<UUID>()
    @State private var isSelectionMode = false
    
    var filteredNotifications: [NotificationItem] {
        var notifications = appState.notifications
        
        // Filter by type
        if selectedFilter != .all {
            notifications = notifications.filter { notification in
                switch selectedFilter {
                case .unread:
                    return !notification.isRead
                case .important:
                    return notification.priority == .high || notification.priority == .critical
                case .system:
                    return notification.category == .system
                case .docker:
                    return notification.category == .docker
                case .services:
                    return notification.category == .service
                default:
                    return true
                }
            }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            notifications = notifications.filter { notification in
                notification.title.localizedCaseInsensitiveContains(searchText) ||
                notification.message.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Sort by timestamp (newest first)
        return notifications.sorted { $0.timestamp > $1.timestamp }
    }
    
    var unreadCount: Int {
        appState.notifications.filter { !$0.isRead }.count
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            HeaderView()
            
            // Notifications content
            NotificationsContentView()
        }
        .sheet(isPresented: $showingSettings) {
            NotificationSettingsView()
        }
    }
    
    @ViewBuilder
    private func HeaderView() -> some View {
        VStack(spacing: 12) {
            // First row: Filter and search
            HStack {
                // Filter picker
                Picker("Filter", selection: $selectedFilter) {
                    ForEach(NotificationFilter.allCases, id: \.self) { filter in
                        HStack {
                            Text(filter.displayName)
                            if filter == .unread && unreadCount > 0 {
                                Text("(\(unreadCount))")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .tag(filter)
                    }
                }
                .pickerStyle(.segmented)
                .frame(width: 400)
                
                Spacer()
                
                // Search field
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search notifications...", text: $searchText)
                        .textFieldStyle(.plain)
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(NSColor.textBackgroundColor))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                .frame(width: 250)
            }
            
            // Second row: Actions
            HStack {
                // Selection mode toggle
                Button(isSelectionMode ? "Cancel" : "Select") {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isSelectionMode.toggle()
                        if !isSelectionMode {
                            selectedNotifications.removeAll()
                        }
                    }
                }
                .buttonStyle(.bordered)
                
                if isSelectionMode {
                    // Bulk actions
                    Button("Select All") {
                        selectedNotifications = Set(filteredNotifications.map { $0.id })
                    }
                    .buttonStyle(.bordered)
                    .disabled(filteredNotifications.isEmpty)
                    
                    Button("Mark Read") {
                        markSelectedAsRead()
                    }
                    .buttonStyle(.bordered)
                    .disabled(selectedNotifications.isEmpty)
                    
                    Button("Delete") {
                        deleteSelected()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                    .disabled(selectedNotifications.isEmpty)
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 8) {
                    Button("Mark All Read") {
                        markAllAsRead()
                    }
                    .buttonStyle(.bordered)
                    .disabled(unreadCount == 0)
                    
                    Button("Clear All") {
                        clearAllNotifications()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                    .disabled(appState.notifications.isEmpty)
                    
                    Button("Settings") {
                        showingSettings = true
                    }
                    .buttonStyle(.bordered)
                    
                    Button(action: { appState.refreshNotifications() }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .buttonStyle(.borderless)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func NotificationsContentView() -> some View {
        if filteredNotifications.isEmpty {
            EmptyStateView()
        } else {
            NotificationsListView()
        }
    }
    
    @ViewBuilder
    private func EmptyStateView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "bell.slash")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Notifications")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(searchText.isEmpty ? "You're all caught up!" : "No notifications match your search")
                .font(.body)
                .foregroundColor(.secondary)
            
            if !searchText.isEmpty {
                Button("Clear Search") {
                    searchText = ""
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private func NotificationsListView() -> some View {
        List {
            ForEach(filteredNotifications) { notification in
                NotificationRowView(
                    notification: notification,
                    isSelected: selectedNotifications.contains(notification.id),
                    isSelectionMode: isSelectionMode,
                    onToggleSelection: {
                        toggleSelection(for: notification.id)
                    },
                    onMarkAsRead: {
                        markAsRead(notification.id)
                    },
                    onDelete: {
                        deleteNotification(notification.id)
                    }
                )
            }
        }
        .listStyle(.plain)
    }
    
    // MARK: - Actions
    
    private func toggleSelection(for id: UUID) {
        if selectedNotifications.contains(id) {
            selectedNotifications.remove(id)
        } else {
            selectedNotifications.insert(id)
        }
    }
    
    private func markAsRead(_ id: UUID) {
        appState.markNotificationAsRead(id)
    }
    
    private func deleteNotification(_ id: UUID) {
        appState.deleteNotification(id)
    }
    
    private func markSelectedAsRead() {
        for id in selectedNotifications {
            appState.markNotificationAsRead(id)
        }
        selectedNotifications.removeAll()
    }
    
    private func deleteSelected() {
        for id in selectedNotifications {
            appState.deleteNotification(id)
        }
        selectedNotifications.removeAll()
    }
    
    private func markAllAsRead() {
        appState.markAllNotificationsAsRead()
    }
    
    private func clearAllNotifications() {
        appState.clearAllNotifications()
    }
}

struct NotificationRowView: View {
    let notification: NotificationItem
    let isSelected: Bool
    let isSelectionMode: Bool
    let onToggleSelection: () -> Void
    let onMarkAsRead: () -> Void
    let onDelete: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Selection checkbox (only in selection mode)
            if isSelectionMode {
                Button(action: onToggleSelection) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .accentColor : .secondary)
                }
                .buttonStyle(.plain)
            }
            
            // Priority indicator
            Rectangle()
                .fill(notification.priority.color)
                .frame(width: 4)
                .clipShape(RoundedRectangle(cornerRadius: 2))
            
            // Notification content
            VStack(alignment: .leading, spacing: 8) {
                // Header with title and timestamp
                HStack {
                    HStack(spacing: 8) {
                        // Category icon
                        Image(systemName: notification.category.iconName)
                            .foregroundColor(notification.category.color)
                            .frame(width: 16)
                        
                        // Title
                        Text(notification.title)
                            .font(.headline)
                            .fontWeight(notification.isRead ? .medium : .semibold)
                            .lineLimit(1)
                        
                        // Unread indicator
                        if !notification.isRead {
                            Circle()
                                .fill(.blue)
                                .frame(width: 8, height: 8)
                        }
                    }
                    
                    Spacer()
                    
                    // Timestamp
                    Text(notification.timestamp.timeAgoDisplay)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Message
                Text(notification.message)
                    .font(.body)
                    .foregroundColor(notification.isRead ? .secondary : .primary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
                
                // Actions (if any)
                if !notification.actions.isEmpty {
                    HStack(spacing: 8) {
                        ForEach(notification.actions, id: \.title) { action in
                            Button(action.title) {
                                handleAction(action)
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.small)
                        }
                    }
                }
            }
            
            Spacer()
            
            // Action buttons (shown on hover or in selection mode)
            if isHovered || isSelectionMode {
                VStack(spacing: 4) {
                    if !notification.isRead {
                        Button(action: onMarkAsRead) {
                            Image(systemName: "envelope.open")
                        }
                        .buttonStyle(.borderless)
                        .help("Mark as read")
                    }
                    
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                    .buttonStyle(.borderless)
                    .help("Delete")
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(notification.isRead ? Color.clear : Color.accentColor.opacity(0.05))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 2)
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .contextMenu {
            if !notification.isRead {
                Button("Mark as Read") {
                    onMarkAsRead()
                }
            }
            
            Button("Delete") {
                onDelete()
            }
            
            Divider()
            
            Button("Copy Message") {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(notification.message, forType: .string)
            }
        }
    }
    
    private func handleAction(_ action: NotificationAction) {
        // Handle notification actions
        switch action.type {
        case .url:
            if let url = URL(string: action.data) {
                NSWorkspace.shared.open(url)
            }
        case .command:
            // Execute command
            break
        case .dismiss:
            onMarkAsRead()
        }
    }
}

struct NotificationSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("notificationsEnabled") private var notificationsEnabled = true
    @AppStorage("soundEnabled") private var soundEnabled = true
    @AppStorage("badgeEnabled") private var badgeEnabled = true
    @AppStorage("systemNotifications") private var systemNotifications = true
    @AppStorage("dockerNotifications") private var dockerNotifications = true
    @AppStorage("serviceNotifications") private var serviceNotifications = true
    @AppStorage("criticalOnly") private var criticalOnly = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                Text("Notification Settings")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Done") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
            
            Divider()
            
            // General settings
            VStack(alignment: .leading, spacing: 12) {
                Text("General")
                    .font(.headline)
                
                Toggle("Enable Notifications", isOn: $notificationsEnabled)
                Toggle("Play Sound", isOn: $soundEnabled)
                    .disabled(!notificationsEnabled)
                Toggle("Show Badge Count", isOn: $badgeEnabled)
                    .disabled(!notificationsEnabled)
                Toggle("Critical Notifications Only", isOn: $criticalOnly)
                    .disabled(!notificationsEnabled)
            }
            
            Divider()
            
            // Category settings
            VStack(alignment: .leading, spacing: 12) {
                Text("Categories")
                    .font(.headline)
                
                Toggle("System Notifications", isOn: $systemNotifications)
                    .disabled(!notificationsEnabled)
                Toggle("Docker Notifications", isOn: $dockerNotifications)
                    .disabled(!notificationsEnabled)
                Toggle("Service Notifications", isOn: $serviceNotifications)
                    .disabled(!notificationsEnabled)
            }
            
            Spacer()
            
            // Test notification button
            HStack {
                Spacer()
                
                Button("Send Test Notification") {
                    sendTestNotification()
                }
                .buttonStyle(.bordered)
                .disabled(!notificationsEnabled)
                
                Spacer()
            }
        }
        .padding()
        .frame(width: 400, height: 500)
    }
    
    private func sendTestNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Test Notification"
        content.body = "This is a test notification from WOW macOS."
        content.sound = soundEnabled ? .default : nil
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        )
        
        UNUserNotificationCenter.current().add(request)
    }
}

// MARK: - Supporting Types

enum NotificationFilter: CaseIterable {
    case all
    case unread
    case important
    case system
    case docker
    case services
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .unread: return "Unread"
        case .important: return "Important"
        case .system: return "System"
        case .docker: return "Docker"
        case .services: return "Services"
        }
    }
}

extension NotificationCategory {
    var iconName: String {
        switch self {
        case .system: return "gear"
        case .service: return "server.rack"
        case .docker: return "shippingbox"
        case .network: return "network"
        case .security: return "shield"
        case .update: return "arrow.down.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .system: return .blue
        case .service: return .green
        case .docker: return .purple
        case .network: return .orange
        case .security: return .red
        case .update: return .cyan
        }
    }
}

extension NotificationPriority {
    var color: Color {
        switch self {
        case .low: return .gray
        case .normal: return .blue
        case .high: return .orange
        case .critical: return .red
        }
    }
}

extension Date {
    var timeAgoDisplay: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(self)
        
        if timeInterval < 60 {
            return "Just now"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)m ago"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)h ago"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)d ago"
        }
    }
}

// MARK: - AppState Extension

extension AppState {
    func markNotificationAsRead(_ id: UUID) {
        DispatchQueue.main.async {
            if let index = self.notifications.firstIndex(where: { $0.id == id }) {
                self.notifications[index].isRead = true
            }
        }
    }
    
    func deleteNotification(_ id: UUID) {
        DispatchQueue.main.async {
            self.notifications.removeAll { $0.id == id }
        }
    }
    
    func markAllNotificationsAsRead() {
        DispatchQueue.main.async {
            for index in self.notifications.indices {
                self.notifications[index].isRead = true
            }
        }
    }
    
    func clearAllNotifications() {
        DispatchQueue.main.async {
            self.notifications.removeAll()
        }
    }
    
    func refreshNotifications() {
        // This would typically fetch notifications from the backend
        Task {
            await fetchNotifications()
        }
    }
    
    private func fetchNotifications() async {
        // Implementation would fetch notifications from backend API
        // This is a placeholder for the actual implementation
    }
}

#Preview {
    NotificationsView()
        .environmentObject({
            let appState = AppState()
            
            // Add sample notifications for preview
            appState.notifications = [
                NotificationItem(
                    id: UUID(),
                    message: "The backend service needs to be restarted due to configuration changes.",
                    type: .warning,
                    timestamp: Date(),
                    isRead: false
                ),
                NotificationItem(
                    id: UUID(),
                    message: "Container 'web-server' has stopped unexpectedly.",
                    type: .error,
                    timestamp: Date(),
                    isRead: false
                ),
                NotificationItem(
                    id: UUID(),
                    message: "A new system update is available for installation.",
                    type: .info,
                    timestamp: Date(),
                    isRead: true
                )
            ]
            
            return appState
        }())
        .frame(width: 1000, height: 700)
}