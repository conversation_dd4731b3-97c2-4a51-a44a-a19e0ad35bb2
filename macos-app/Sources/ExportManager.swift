import Foundation
import SwiftUI
import UniformTypeIdentifiers
import PDFKit

// MARK: - Export Models

enum ExportFormat: String, CaseIterable {
    case json = "JSON"
    case csv = "CSV"
    case txt = "Text"
    case pdf = "PDF"
    case html = "HTML"
    
    var fileExtension: String {
        switch self {
        case .json: return "json"
        case .csv: return "csv"
        case .txt: return "txt"
        case .pdf: return "pdf"
        case .html: return "html"
        }
    }
    
    var utType: UTType {
        switch self {
        case .json: return .json
        case .csv: return .commaSeparatedText
        case .txt: return .plainText
        case .pdf: return .pdf
        case .html: return .html
        }
    }
    
    var mimeType: String {
        switch self {
        case .json: return "application/json"
        case .csv: return "text/csv"
        case .txt: return "text/plain"
        case .pdf: return "application/pdf"
        case .html: return "text/html"
        }
    }
}

enum ExportDataType: String, CaseIterable {
    case logs = "Logs"
    case services = "Services"
    case dockerContainers = "Docker Containers"
    case systemMetrics = "System Metrics"
    case notifications = "Notifications"
    case systemReport = "System Report"
    case configuration = "Configuration"
    
    var icon: String {
        switch self {
        case .logs: return "doc.text"
        case .services: return "gear"
        case .dockerContainers: return "cube"
        case .systemMetrics: return "chart.bar"
        case .notifications: return "bell"
        case .systemReport: return "doc.richtext"
        case .configuration: return "gearshape"
        }
    }
}

struct ExportOptions {
    var format: ExportFormat = .json
    var includeTimestamps: Bool = true
    var includeMetadata: Bool = true
    var dateRange: DateRange?
    var filters: [String: Any] = [:]
    var customFields: [String] = []
    var compressionEnabled: Bool = false
    
    struct DateRange {
        let start: Date
        let end: Date
    }
}

struct ExportResult {
    let success: Bool
    let filePath: URL?
    let error: Error?
    let fileSize: Int64?
    let recordCount: Int?
}

// MARK: - Export Manager

@MainActor
class ExportManager: ObservableObject {
    static let shared = ExportManager()
    
    @Published var isExporting = false
    @Published var exportProgress: Double = 0.0
    @Published var currentExportTask: String = ""
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
    
    private let isoDateFormatter: ISO8601DateFormatter = {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter
    }()
    
    private init() {}
    
    // MARK: - Main Export Functions
    
    func exportData(
        type: ExportDataType,
        options: ExportOptions,
        appState: AppState
    ) async -> ExportResult {
        isExporting = true
        exportProgress = 0.0
        currentExportTask = "Preparing export..."
        
        defer {
            isExporting = false
            exportProgress = 0.0
            currentExportTask = ""
        }
        
        do {
            let data = try await prepareData(type: type, options: options, appState: appState)
            exportProgress = 0.5
            
            currentExportTask = "Formatting data..."
            let formattedData = try formatData(data, format: options.format, type: type, options: options)
            exportProgress = 0.8
            
            currentExportTask = "Saving file..."
            let filePath = try await saveToFile(formattedData, type: type, format: options.format, options: options)
            exportProgress = 1.0
            
            let fileSize = try FileManager.default.attributesOfItem(atPath: filePath.path)[.size] as? Int64
            
            return ExportResult(
                success: true,
                filePath: filePath,
                error: nil,
                fileSize: fileSize,
                recordCount: getRecordCount(data)
            )
        } catch {
            return ExportResult(
                success: false,
                filePath: nil,
                error: error,
                fileSize: nil,
                recordCount: nil
            )
        }
    }
    
    func exportSystemReport(appState: AppState, options: ExportOptions) async -> ExportResult {
        isExporting = true
        exportProgress = 0.0
        currentExportTask = "Generating system report..."
        
        defer {
            isExporting = false
            exportProgress = 0.0
            currentExportTask = ""
        }
        
        do {
            let report = try await generateSystemReport(appState: appState)
            exportProgress = 0.7
            
            currentExportTask = "Formatting report..."
            let formattedReport = try formatSystemReport(report, format: options.format)
            exportProgress = 0.9
            
            currentExportTask = "Saving report..."
            let filePath = try await saveToFile(formattedReport, type: .systemReport, format: options.format, options: options)
            exportProgress = 1.0
            
            let fileSize = try FileManager.default.attributesOfItem(atPath: filePath.path)[.size] as? Int64
            
            return ExportResult(
                success: true,
                filePath: filePath,
                error: nil,
                fileSize: fileSize,
                recordCount: nil
            )
        } catch {
            return ExportResult(
                success: false,
                filePath: nil,
                error: error,
                fileSize: nil,
                recordCount: nil
            )
        }
    }
    
    // MARK: - Data Preparation
    
    private func prepareData(
        type: ExportDataType,
        options: ExportOptions,
        appState: AppState
    ) async throws -> [String: Any] {
        switch type {
        case .logs:
            return try await prepareLogs(options: options, appState: appState)
        case .services:
            return try prepareServices(options: options, appState: appState)
        case .dockerContainers:
            return try prepareDockerContainers(options: options, appState: appState)
        case .systemMetrics:
            return try prepareSystemMetrics(options: options, appState: appState)
        case .notifications:
            return try prepareNotifications(options: options, appState: appState)
        case .configuration:
            return try prepareConfiguration(options: options, appState: appState)
        case .systemReport:
            return try await generateSystemReport(appState: appState)
        }
    }
    
    private func prepareLogs(options: ExportOptions, appState: AppState) async throws -> [String: Any] {
        currentExportTask = "Fetching logs..."
        
        // In a real implementation, this would fetch logs from the backend
        // For now, we'll create sample log data
        var logs: [[String: Any]] = []
        
        // Sample log entries
        let sampleLogs = [
            "INFO: Service started successfully",
            "WARN: High memory usage detected",
            "ERROR: Failed to connect to database",
            "DEBUG: Processing request",
            "INFO: Service stopped"
        ]
        
        for (index, message) in sampleLogs.enumerated() {
            var logEntry: [String: Any] = [
                "id": index + 1,
                "message": message,
                "level": message.components(separatedBy: ":").first ?? "INFO",
                "timestamp": isoDateFormatter.string(from: Date().addingTimeInterval(-Double(index * 3600)))
            ]
            
            if options.includeMetadata {
                logEntry["source"] = "system"
                logEntry["thread"] = "main"
                logEntry["file"] = "app.log"
            }
            
            logs.append(logEntry)
        }
        
        return [
            "logs": logs,
            "total_count": logs.count,
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func prepareServices(options: ExportOptions, appState: AppState) throws -> [String: Any] {
        currentExportTask = "Preparing services data..."
        
        let services = appState.services.map { service in
            var serviceData: [String: Any] = [
                "name": service.name,
                "status": service.status.rawValue,
                "port": service.port,
                "url": service.url
            ]
            
            if options.includeTimestamps {
                serviceData["last_updated"] = isoDateFormatter.string(from: Date())
            }
            
            if options.includeMetadata {
                serviceData["health_check_url"] = service.healthCheckUrl
                serviceData["description"] = service.description
            }
            
            return serviceData
        }
        
        return [
            "services": services,
            "total_count": services.count,
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func prepareDockerContainers(options: ExportOptions, appState: AppState) throws -> [String: Any] {
        currentExportTask = "Preparing Docker containers data..."
        
        let containers = appState.dockerContainers.map { container in
            var containerData: [String: Any] = [
                "id": container.id,
                "name": container.name,
                "image": container.image,
                "state": container.state,
                "status": container.status
            ]
            
            if options.includeTimestamps {
                containerData["created"] = isoDateFormatter.string(from: container.created)
            }
            
            if options.includeMetadata {
                containerData["ports"] = container.ports
                containerData["environment"] = container.environment
                containerData["labels"] = container.labels
            }
            
            return containerData
        }
        
        return [
            "containers": containers,
            "total_count": containers.count,
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func prepareSystemMetrics(options: ExportOptions, appState: AppState) throws -> [String: Any] {
        currentExportTask = "Preparing system metrics data..."
        
        guard let metrics = appState.systemMetrics else {
            throw ExportError.noDataAvailable
        }
        
        var metricsData: [String: Any] = [
            "cpu": [
                "usage": metrics.cpu.usage,
                "cores": metrics.cpu.cores,
                "load_average": [
                    "one_minute": metrics.loadAverage.oneMinute,
                    "five_minutes": metrics.loadAverage.fiveMinutes,
                    "fifteen_minutes": metrics.loadAverage.fifteenMinutes
                ]
            ],
            "memory": [
                "total": metrics.memory.total,
                "used": metrics.memory.used,
                "free": metrics.memory.free,
                "used_percentage": metrics.memory.usedPercentage
            ],
            "disk": [
                "total": metrics.disk.total,
                "used": metrics.disk.used,
                "free": metrics.disk.free,
                "used_percentage": metrics.disk.usedPercentage
            ],
            "network": [
                "bytes_sent": metrics.network.bytesSent,
                "bytes_received": metrics.network.bytesReceived,
                "packets_sent": metrics.network.packetsSent,
                "packets_received": metrics.network.packetsReceived
            ]
        ]
        
        if options.includeTimestamps {
            metricsData["timestamp"] = isoDateFormatter.string(from: Date())
        }
        
        return [
            "system_metrics": metricsData,
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func prepareNotifications(options: ExportOptions, appState: AppState) throws -> [String: Any] {
        currentExportTask = "Preparing notifications data..."
        
        let notifications = appState.notifications.map { notification in
            var notificationData: [String: Any] = [
                "id": notification.id.uuidString,
                "title": notification.title,
                "message": notification.message,
                "category": notification.category.rawValue,
                "priority": notification.priority.rawValue,
                "is_read": notification.isRead
            ]
            
            if options.includeTimestamps {
                notificationData["timestamp"] = isoDateFormatter.string(from: notification.timestamp)
            }
            
            if options.includeMetadata {
                notificationData["metadata"] = notification.metadata
            }
            
            return notificationData
        }
        
        return [
            "notifications": notifications,
            "total_count": notifications.count,
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func prepareConfiguration(options: ExportOptions, appState: AppState) throws -> [String: Any] {
        currentExportTask = "Preparing configuration data..."
        
        return [
            "server_url": appState.serverURL,
            "connection_status": appState.isConnected ? "connected" : "disconnected",
            "auto_refresh_interval": 30, // From settings
            "notification_settings": [
                "enabled": true,
                "sound_enabled": true,
                "badge_count": true
            ],
            "export_timestamp": isoDateFormatter.string(from: Date())
        ]
    }
    
    private func generateSystemReport(appState: AppState) async throws -> [String: Any] {
        currentExportTask = "Generating comprehensive system report..."
        
        let services = try prepareServices(options: ExportOptions(), appState: appState)
        let containers = try prepareDockerContainers(options: ExportOptions(), appState: appState)
        let metrics = try prepareSystemMetrics(options: ExportOptions(), appState: appState)
        let notifications = try prepareNotifications(options: ExportOptions(), appState: appState)
        
        return [
            "report_title": "WOW Monitor System Report",
            "generated_at": isoDateFormatter.string(from: Date()),
            "system_overview": [
                "total_services": appState.services.count,
                "running_services": appState.services.filter { $0.status == .running }.count,
                "total_containers": appState.dockerContainers.count,
                "running_containers": appState.dockerContainers.filter { $0.state == "running" }.count,
                "unread_notifications": appState.notifications.filter { !$0.isRead }.count
            ],
            "services": services,
            "docker_containers": containers,
            "system_metrics": metrics,
            "notifications": notifications
        ]
    }
    
    // MARK: - Data Formatting
    
    private func formatData(
        _ data: [String: Any],
        format: ExportFormat,
        type: ExportDataType,
        options: ExportOptions
    ) throws -> Data {
        switch format {
        case .json:
            return try formatAsJSON(data)
        case .csv:
            return try formatAsCSV(data, type: type)
        case .txt:
            return try formatAsText(data, type: type)
        case .pdf:
            return try formatAsPDF(data, type: type)
        case .html:
            return try formatAsHTML(data, type: type)
        }
    }
    
    private func formatAsJSON(_ data: [String: Any]) throws -> Data {
        return try JSONSerialization.data(withJSONObject: data, options: [.prettyPrinted, .sortedKeys])
    }
    
    private func formatAsCSV(_ data: [String: Any], type: ExportDataType) throws -> Data {
        var csvContent = ""
        
        switch type {
        case .logs:
            if let logs = data["logs"] as? [[String: Any]] {
                csvContent = "ID,Timestamp,Level,Message,Source\n"
                for log in logs {
                    let id = log["id"] ?? ""
                    let timestamp = log["timestamp"] ?? ""
                    let level = log["level"] ?? ""
                    let message = (log["message"] as? String ?? "").replacingOccurrences(of: ",", with: ";")
                    let source = log["source"] ?? ""
                    csvContent += "\(id),\(timestamp),\(level),\"\(message)\",\(source)\n"
                }
            }
            
        case .services:
            if let services = data["services"] as? [[String: Any]] {
                csvContent = "Name,Status,Port,URL\n"
                for service in services {
                    let name = service["name"] ?? ""
                    let status = service["status"] ?? ""
                    let port = service["port"] ?? ""
                    let url = service["url"] ?? ""
                    csvContent += "\(name),\(status),\(port),\(url)\n"
                }
            }
            
        case .dockerContainers:
            if let containers = data["containers"] as? [[String: Any]] {
                csvContent = "ID,Name,Image,State,Status\n"
                for container in containers {
                    let id = container["id"] ?? ""
                    let name = container["name"] ?? ""
                    let image = container["image"] ?? ""
                    let state = container["state"] ?? ""
                    let status = container["status"] ?? ""
                    csvContent += "\(id),\(name),\(image),\(state),\(status)\n"
                }
            }
            
        default:
            throw ExportError.unsupportedFormat
        }
        
        return csvContent.data(using: .utf8) ?? Data()
    }
    
    private func formatAsText(_ data: [String: Any], type: ExportDataType) throws -> Data {
        var textContent = "WOW Monitor Export - \(type.rawValue)\n"
        textContent += "Generated: \(Date())\n\n"
        
        switch type {
        case .logs:
            if let logs = data["logs"] as? [[String: Any]] {
                textContent += "LOGS\n" + String(repeating: "=", count: 50) + "\n\n"
                for log in logs {
                    let timestamp = log["timestamp"] ?? ""
                    let level = log["level"] ?? ""
                    let message = log["message"] ?? ""
                    textContent += "[\(timestamp)] \(level): \(message)\n"
                }
            }
            
        case .services:
            if let services = data["services"] as? [[String: Any]] {
                textContent += "SERVICES\n" + String(repeating: "=", count: 50) + "\n\n"
                for service in services {
                    let name = service["name"] ?? ""
                    let status = service["status"] ?? ""
                    let port = service["port"] ?? ""
                    let url = service["url"] ?? ""
                    textContent += "Service: \(name)\n"
                    textContent += "Status: \(status)\n"
                    textContent += "Port: \(port)\n"
                    textContent += "URL: \(url)\n\n"
                }
            }
            
        default:
            textContent += JSONSerialization.isValidJSONObject(data) ?
                String(data: try JSONSerialization.data(withJSONObject: data, options: .prettyPrinted), encoding: .utf8) ?? "" :
                "\(data)"
        }
        
        return textContent.data(using: .utf8) ?? Data()
    }
    
    private func formatAsPDF(_ data: [String: Any], type: ExportDataType) throws -> Data {
        let htmlContent = try formatAsHTML(data, type: type)
        
        // Convert HTML to PDF using WebKit (simplified implementation)
        // In a real app, you'd use WKWebView or PDFKit for better PDF generation
        let pdfData = NSMutableData()
        
        // This is a simplified PDF creation - in practice, you'd want to use proper PDF generation
        let pdfContent = "PDF Export of \(type.rawValue)\n\nGenerated: \(Date())\n\n" +
                        (String(data: htmlContent, encoding: .utf8) ?? "")
        
        if let data = pdfContent.data(using: .utf8) {
            pdfData.append(data)
        }
        
        return pdfData as Data
    }
    
    private func formatAsHTML(_ data: [String: Any], type: ExportDataType) throws -> Data {
        var html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>WOW Monitor Export - \(type.rawValue)</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .timestamp { font-size: 0.9em; color: #666; }
            </style>
        </head>
        <body>
            <h1>WOW Monitor Export - \(type.rawValue)</h1>
            <p class="timestamp">Generated: \(Date())</p>
        """
        
        switch type {
        case .services:
            if let services = data["services"] as? [[String: Any]] {
                html += """
                <h2>Services (\(services.count))</h2>
                <table>
                    <tr><th>Name</th><th>Status</th><th>Port</th><th>URL</th></tr>
                """
                for service in services {
                    let name = service["name"] ?? ""
                    let status = service["status"] ?? ""
                    let port = service["port"] ?? ""
                    let url = service["url"] ?? ""
                    html += "<tr><td>\(name)</td><td>\(status)</td><td>\(port)</td><td>\(url)</td></tr>"
                }
                html += "</table>"
            }
            
        case .dockerContainers:
            if let containers = data["containers"] as? [[String: Any]] {
                html += """
                <h2>Docker Containers (\(containers.count))</h2>
                <table>
                    <tr><th>Name</th><th>Image</th><th>State</th><th>Status</th></tr>
                """
                for container in containers {
                    let name = container["name"] ?? ""
                    let image = container["image"] ?? ""
                    let state = container["state"] ?? ""
                    let status = container["status"] ?? ""
                    html += "<tr><td>\(name)</td><td>\(image)</td><td>\(state)</td><td>\(status)</td></tr>"
                }
                html += "</table>"
            }
            
        default:
            html += "<pre>\(data)</pre>"
        }
        
        html += "</body></html>"
        
        return html.data(using: .utf8) ?? Data()
    }
    
    private func formatSystemReport(_ data: [String: Any], format: ExportFormat) throws -> Data {
        switch format {
        case .json:
            return try formatAsJSON(data)
        case .html:
            return try formatSystemReportAsHTML(data)
        case .pdf:
            let htmlData = try formatSystemReportAsHTML(data)
            return htmlData // In practice, convert HTML to PDF
        default:
            return try formatAsText(data, type: .systemReport)
        }
    }
    
    private func formatSystemReportAsHTML(_ data: [String: Any]) throws -> Data {
        let title = data["report_title"] as? String ?? "System Report"
        let generatedAt = data["generated_at"] as? String ?? ""
        
        var html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>\(title)</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
                h2 { color: #34495e; margin-top: 30px; }
                .overview { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .metric { display: inline-block; margin: 10px; padding: 10px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: left; }
                th { background-color: #3498db; color: white; }
                .status-running { color: #27ae60; font-weight: bold; }
                .status-stopped { color: #e74c3c; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>\(title)</h1>
            <p><strong>Generated:</strong> \(generatedAt)</p>
        """
        
        // Add system overview
        if let overview = data["system_overview"] as? [String: Any] {
            html += """
            <div class="overview">
                <h2>System Overview</h2>
                <div class="metric">Services: \(overview["running_services"] ?? 0)/\(overview["total_services"] ?? 0) running</div>
                <div class="metric">Containers: \(overview["running_containers"] ?? 0)/\(overview["total_containers"] ?? 0) running</div>
                <div class="metric">Unread Notifications: \(overview["unread_notifications"] ?? 0)</div>
            </div>
            """
        }
        
        html += "</body></html>"
        
        return html.data(using: .utf8) ?? Data()
    }
    
    // MARK: - File Operations
    
    private func saveToFile(
        _ data: Data,
        type: ExportDataType,
        format: ExportFormat,
        options: ExportOptions
    ) async throws -> URL {
        let fileName = generateFileName(type: type, format: format)
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filePath = documentsPath.appendingPathComponent(fileName)
        
        try data.write(to: filePath)
        
        if options.compressionEnabled && format != .pdf {
            return try await compressFile(filePath)
        }
        
        return filePath
    }
    
    private func generateFileName(type: ExportDataType, format: ExportFormat) -> String {
        let timestamp = dateFormatter.string(from: Date())
        let typeString = type.rawValue.lowercased().replacingOccurrences(of: " ", with: "_")
        return "wow_\(typeString)_\(timestamp).\(format.fileExtension)"
    }
    
    private func compressFile(_ filePath: URL) async throws -> URL {
        // Implementation for file compression (e.g., zip)
        // For now, return the original file
        return filePath
    }
    
    private func getRecordCount(_ data: [String: Any]) -> Int? {
        if let logs = data["logs"] as? [Any] {
            return logs.count
        } else if let services = data["services"] as? [Any] {
            return services.count
        } else if let containers = data["containers"] as? [Any] {
            return containers.count
        } else if let notifications = data["notifications"] as? [Any] {
            return notifications.count
        }
        return nil
    }
}

// MARK: - Export Errors

enum ExportError: LocalizedError {
    case noDataAvailable
    case unsupportedFormat
    case fileWriteError
    case compressionError
    
    var errorDescription: String? {
        switch self {
        case .noDataAvailable:
            return "No data available for export"
        case .unsupportedFormat:
            return "Unsupported export format"
        case .fileWriteError:
            return "Failed to write file"
        case .compressionError:
            return "Failed to compress file"
        }
    }
}

// MARK: - Export View

struct ExportView: View {
    @EnvironmentObject private var appState: AppState
    @StateObject private var exportManager = ExportManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedDataType: ExportDataType = .logs
    @State private var selectedFormat: ExportFormat = .json
    @State private var exportOptions = ExportOptions()
    @State private var showingFilePicker = false
    @State private var exportResult: ExportResult?
    @State private var showingResult = false
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Text("Export Data")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Cancel") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            
            Divider()
            
            // Export configuration
            VStack(alignment: .leading, spacing: 16) {
                // Data type selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Data Type")
                        .font(.headline)
                    
                    Picker("Data Type", selection: $selectedDataType) {
                        ForEach(ExportDataType.allCases, id: \.self) { type in
                            Label(type.rawValue, systemImage: type.icon)
                                .tag(type)
                        }
                    }
                    .pickerStyle(.menu)
                }
                
                // Format selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Format")
                        .font(.headline)
                    
                    Picker("Format", selection: $selectedFormat) {
                        ForEach(ExportFormat.allCases, id: \.self) { format in
                            Text(format.rawValue)
                                .tag(format)
                        }
                    }
                    .pickerStyle(.segmented)
                }
                
                // Options
                VStack(alignment: .leading, spacing: 8) {
                    Text("Options")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Toggle("Include Timestamps", isOn: $exportOptions.includeTimestamps)
                        Toggle("Include Metadata", isOn: $exportOptions.includeMetadata)
                        Toggle("Enable Compression", isOn: $exportOptions.compressionEnabled)
                    }
                }
            }
            
            Spacer()
            
            // Export progress
            if exportManager.isExporting {
                VStack(spacing: 8) {
                    ProgressView(value: exportManager.exportProgress)
                        .progressViewStyle(.linear)
                    
                    Text(exportManager.currentExportTask)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Export button
            HStack {
                Spacer()
                
                Button("Export") {
                    performExport()
                }
                .buttonStyle(.borderedProminent)
                .disabled(exportManager.isExporting)
            }
        }
        .padding()
        .frame(width: 500, height: 400)
        .alert("Export Complete", isPresented: $showingResult) {
            if let result = exportResult, result.success {
                Button("Show in Finder") {
                    if let filePath = result.filePath {
                        NSWorkspace.shared.selectFile(filePath.path, inFileViewerRootedAtPath: "")
                    }
                }
                Button("OK") {
                    dismiss()
                }
            } else {
                Button("OK") {}
            }
        } message: {
            if let result = exportResult {
                if result.success {
                    Text("Export completed successfully. File saved to: \(result.filePath?.lastPathComponent ?? "Unknown")")
                } else {
                    Text("Export failed: \(result.error?.localizedDescription ?? "Unknown error")")
                }
            }
        }
    }
    
    private func performExport() {
        Task {
            let result = await exportManager.exportData(
                type: selectedDataType,
                options: exportOptions,
                appState: appState
            )
            
            await MainActor.run {
                exportResult = result
                showingResult = true
            }
        }
    }
}

#Preview {
    ExportView()
        .environmentObject(AppState.shared)
}