import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedTab: SettingsTab = .general
    
    var body: some View {
        NavigationSplitView {
            // Sidebar with settings categories
            List(SettingsTab.allCases, id: \.self, selection: $selectedTab) { tab in
                Label(tab.displayName, systemImage: tab.iconName)
                    .tag(tab)
            }
            .listStyle(.sidebar)
            .frame(minWidth: 200)
        } detail: {
            // Settings content based on selected tab
            Group {
                switch selectedTab {
                case .general:
                    GeneralSettingsView()
                case .connection:
                    ConnectionSettingsView()
                case .notifications:
                    NotificationSettingsView()
                case .appearance:
                    AppearanceSettingsView()
                case .advanced:
                    AdvancedSettingsView()
                case .about:
                    AboutSettingsView()
                }
            }
            .frame(minWidth: 500, minHeight: 400)
        }
        .navigationTitle("Settings")
    }
}

// MARK: - General Settings

struct GeneralSettingsView: View {
    @AppStorage("launchAtLogin") private var launchAtLogin = false
    @AppStorage("showMenuBarIcon") private var showMenuBarIcon = true
    @AppStorage("hideFromDock") private var hideFromDock = false
    @AppStorage("autoRefreshInterval") private var autoRefreshInterval = 30.0
    @AppStorage("enableAutoRefresh") private var enableAutoRefresh = true
    
    var body: some View {
        Form {
            Section("Startup") {
                Toggle("Launch at Login", isOn: $launchAtLogin)
                    .onChange(of: launchAtLogin) { newValue in
                        setLaunchAtLogin(newValue)
                    }
                
                Toggle("Show Menu Bar Icon", isOn: $showMenuBarIcon)
                
                Toggle("Hide from Dock", isOn: $hideFromDock)
                    .help("Hide the app icon from the Dock (requires restart)")
            }
            
            Section("Data Refresh") {
                Toggle("Enable Auto Refresh", isOn: $enableAutoRefresh)
                
                HStack {
                    Text("Refresh Interval")
                    Spacer()
                    Slider(value: $autoRefreshInterval, in: 5...300, step: 5) {
                        Text("Refresh Interval")
                    } minimumValueLabel: {
                        Text("5s")
                    } maximumValueLabel: {
                        Text("5m")
                    }
                    .frame(width: 200)
                    
                    Text("\(Int(autoRefreshInterval))s")
                        .frame(width: 30)
                        .monospacedDigit()
                }
                .disabled(!enableAutoRefresh)
            }
            
            Section("Data Management") {
                HStack {
                    VStack(alignment: .leading) {
                        Text("Clear Cache")
                        Text("Remove cached data and force refresh")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Clear") {
                        clearCache()
                    }
                    .buttonStyle(.bordered)
                }
                
                HStack {
                    VStack(alignment: .leading) {
                        Text("Reset Settings")
                        Text("Reset all settings to default values")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Reset") {
                        resetSettings()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("General")
    }
    
    private func setLaunchAtLogin(_ enabled: Bool) {
        // Implementation for setting launch at login
        // This would typically use ServiceManagement framework
    }
    
    private func clearCache() {
        // Clear application cache
    }
    
    private func resetSettings() {
        // Reset all settings to defaults
        launchAtLogin = false
        showMenuBarIcon = true
        hideFromDock = false
        autoRefreshInterval = 30.0
        enableAutoRefresh = true
    }
}

// MARK: - Connection Settings

struct ConnectionSettingsView: View {
    @EnvironmentObject var appState: AppState
    @AppStorage("serverURL") private var serverURL = "http://localhost:3000"
    @AppStorage("apiKey") private var apiKey = ""
    @AppStorage("connectionTimeout") private var connectionTimeout = 30.0
    @AppStorage("retryAttempts") private var retryAttempts = 3
    @AppStorage("enableSSL") private var enableSSL = false
    @State private var isTestingConnection = false
    @State private var connectionTestResult: ConnectionTestResult?
    
    var body: some View {
        Form {
            Section("Server Configuration") {
                HStack {
                    Text("Server URL")
                    Spacer()
                    TextField("http://localhost:3000", text: $serverURL)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 250)
                }
                
                HStack {
                    Text("API Key")
                    Spacer()
                    SecureField("Optional API Key", text: $apiKey)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 250)
                }
                
                Toggle("Enable SSL Verification", isOn: $enableSSL)
            }
            
            Section("Connection Settings") {
                HStack {
                    Text("Timeout")
                    Spacer()
                    Slider(value: $connectionTimeout, in: 5...120, step: 5) {
                        Text("Timeout")
                    }
                    .frame(width: 200)
                    
                    Text("\(Int(connectionTimeout))s")
                        .frame(width: 30)
                        .monospacedDigit()
                }
                
                HStack {
                    Text("Retry Attempts")
                    Spacer()
                    Stepper("\(retryAttempts)", value: $retryAttempts, in: 1...10)
                        .frame(width: 100)
                }
            }
            
            Section("Connection Test") {
                HStack {
                    VStack(alignment: .leading) {
                        Text("Test Connection")
                        Text("Verify connection to the backend server")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Test") {
                        testConnection()
                    }
                    .buttonStyle(.bordered)
                    .disabled(isTestingConnection || serverURL.isEmpty)
                }
                
                if let result = connectionTestResult {
                    HStack {
                        Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(result.success ? .green : .red)
                        
                        Text(result.message)
                            .font(.caption)
                        
                        Spacer()
                    }
                    .padding(.top, 4)
                }
                
                if isTestingConnection {
                    HStack {
                        ProgressView()
                            .controlSize(.small)
                        Text("Testing connection...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .padding(.top, 4)
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("Connection")
    }
    
    private func testConnection() {
        isTestingConnection = true
        connectionTestResult = nil
        
        Task {
            do {
                let success = await appState.testConnection(url: serverURL, apiKey: apiKey.isEmpty ? nil : apiKey)
                
                await MainActor.run {
                    isTestingConnection = false
                    connectionTestResult = ConnectionTestResult(
                        success: success,
                        message: success ? "Connection successful" : "Connection failed"
                    )
                }
            } catch {
                await MainActor.run {
                    isTestingConnection = false
                    connectionTestResult = ConnectionTestResult(
                        success: false,
                        message: "Error: \(error.localizedDescription)"
                    )
                }
            }
        }
    }
}

// MARK: - Appearance Settings

struct AppearanceSettingsView: View {
    @AppStorage("colorScheme") private var colorScheme: ColorSchemePreference = .system
    @AppStorage("accentColor") private var accentColor: AccentColorPreference = .blue
    @AppStorage("fontSize") private var fontSize: FontSizePreference = .medium
    @AppStorage("showSidebar") private var showSidebar = true
    @AppStorage("compactMode") private var compactMode = false
    
    var body: some View {
        Form {
            Section("Theme") {
                Picker("Appearance", selection: $colorScheme) {
                    ForEach(ColorSchemePreference.allCases, id: \.self) { scheme in
                        Text(scheme.displayName).tag(scheme)
                    }
                }
                .pickerStyle(.segmented)
                
                Picker("Accent Color", selection: $accentColor) {
                    ForEach(AccentColorPreference.allCases, id: \.self) { color in
                        Label(color.displayName, systemImage: "circle.fill")
                            .foregroundColor(color.color)
                            .tag(color)
                    }
                }
                .pickerStyle(.menu)
            }
            
            Section("Typography") {
                Picker("Font Size", selection: $fontSize) {
                    ForEach(FontSizePreference.allCases, id: \.self) { size in
                        Text(size.displayName).tag(size)
                    }
                }
                .pickerStyle(.segmented)
            }
            
            Section("Layout") {
                Toggle("Show Sidebar", isOn: $showSidebar)
                Toggle("Compact Mode", isOn: $compactMode)
                    .help("Use smaller spacing and controls")
            }
        }
        .formStyle(.grouped)
        .navigationTitle("Appearance")
    }
}

// MARK: - Advanced Settings

struct AdvancedSettingsView: View {
    @AppStorage("enableDebugMode") private var enableDebugMode = false
    @AppStorage("logLevel") private var logLevel: LogLevel = .info
    @AppStorage("maxLogEntries") private var maxLogEntries = 1000
    @AppStorage("enableCrashReporting") private var enableCrashReporting = true
    @AppStorage("enableAnalytics") private var enableAnalytics = false
    
    var body: some View {
        Form {
            Section("Debugging") {
                Toggle("Enable Debug Mode", isOn: $enableDebugMode)
                    .help("Show additional debugging information")
                
                Picker("Log Level", selection: $logLevel) {
                    ForEach(LogLevel.allCases.filter { $0 != .all }, id: \.self) { level in
                        Text(level.displayName).tag(level)
                    }
                }
                .pickerStyle(.menu)
                .disabled(!enableDebugMode)
                
                HStack {
                    Text("Max Log Entries")
                    Spacer()
                    Stepper("\(maxLogEntries)", value: $maxLogEntries, in: 100...10000, step: 100)
                        .frame(width: 120)
                }
            }
            
            Section("Privacy") {
                Toggle("Enable Crash Reporting", isOn: $enableCrashReporting)
                    .help("Send crash reports to help improve the app")
                
                Toggle("Enable Analytics", isOn: $enableAnalytics)
                    .help("Send anonymous usage data")
            }
            
            Section("Developer") {
                HStack {
                    VStack(alignment: .leading) {
                        Text("Export Logs")
                        Text("Export application logs for debugging")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Export") {
                        exportLogs()
                    }
                    .buttonStyle(.bordered)
                }
                
                HStack {
                    VStack(alignment: .leading) {
                        Text("Reset All Data")
                        Text("Clear all application data and settings")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Reset") {
                        resetAllData()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("Advanced")
    }
    
    private func exportLogs() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "wow-logs-\(Date().formatted(date: .numeric, time: .omitted)).txt"
        
        panel.begin { response in
            if response == .OK, let url = panel.url {
                // Export logs to file
            }
        }
    }
    
    private func resetAllData() {
        // Show confirmation dialog and reset all data
    }
}

// MARK: - About Settings

struct AboutSettingsView: View {
    private let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    private let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    
    var body: some View {
        VStack(spacing: 20) {
            // App icon and info
            VStack(spacing: 12) {
                Image(systemName: "app.dashed")
                    .font(.system(size: 64))
                    .foregroundColor(.accentColor)
                
                Text("WOW macOS")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Version \(appVersion) (\(buildNumber))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Divider()
            
            // Description
            VStack(spacing: 8) {
                Text("A native macOS application for managing and monitoring your WOW backend infrastructure.")
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
            }
            
            Divider()
            
            // Links and actions
            VStack(spacing: 12) {
                HStack(spacing: 16) {
                    Button("GitHub Repository") {
                        if let url = URL(string: "https://github.com/your-repo/wow") {
                            NSWorkspace.shared.open(url)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Documentation") {
                        if let url = URL(string: "https://docs.wow.com") {
                            NSWorkspace.shared.open(url)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Report Issue") {
                        if let url = URL(string: "https://github.com/your-repo/wow/issues") {
                            NSWorkspace.shared.open(url)
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                Button("Check for Updates") {
                    checkForUpdates()
                }
                .buttonStyle(.borderedProminent)
            }
            
            Spacer()
            
            // Copyright
            Text("© 2024 WOW Team. All rights reserved.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .navigationTitle("About")
    }
    
    private func checkForUpdates() {
        // Check for application updates
    }
}

// MARK: - Supporting Types

enum SettingsTab: CaseIterable, Hashable {
    case general
    case connection
    case notifications
    case appearance
    case advanced
    case about
    
    var displayName: String {
        switch self {
        case .general: return "General"
        case .connection: return "Connection"
        case .notifications: return "Notifications"
        case .appearance: return "Appearance"
        case .advanced: return "Advanced"
        case .about: return "About"
        }
    }
    
    var iconName: String {
        switch self {
        case .general: return "gear"
        case .connection: return "network"
        case .notifications: return "bell"
        case .appearance: return "paintbrush"
        case .advanced: return "wrench.and.screwdriver"
        case .about: return "info.circle"
        }
    }
}

enum ColorSchemePreference: String, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .system: return "System"
        case .light: return "Light"
        case .dark: return "Dark"
        }
    }
}

enum AccentColorPreference: String, CaseIterable {
    case blue = "blue"
    case purple = "purple"
    case pink = "pink"
    case red = "red"
    case orange = "orange"
    case yellow = "yellow"
    case green = "green"
    case mint = "mint"
    case teal = "teal"
    case cyan = "cyan"
    
    var displayName: String {
        rawValue.capitalized
    }
    
    var color: Color {
        switch self {
        case .blue: return .blue
        case .purple: return .purple
        case .pink: return .pink
        case .red: return .red
        case .orange: return .orange
        case .yellow: return .yellow
        case .green: return .green
        case .mint: return .mint
        case .teal: return .teal
        case .cyan: return .cyan
        }
    }
}

enum FontSizePreference: String, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    
    var displayName: String {
        rawValue.capitalized
    }
}

struct ConnectionTestResult {
    let success: Bool
    let message: String
}

// MARK: - AppState Extension

extension AppState {
    func testConnection(url: String, apiKey: String?) async -> Bool {
        // Implementation for testing connection to backend
        // This would make an actual API call to verify connectivity
        return true // Placeholder
    }
}

#Preview {
    SettingsView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}