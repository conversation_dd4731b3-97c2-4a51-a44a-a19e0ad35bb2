import SwiftUI
import UserNotifications
import Combine

@main
struct WOWMonitorApp: App {
    @StateObject private var appState = AppState()
    @StateObject private var preferencesManager = PreferencesManager.shared
    @StateObject private var notificationManager = NotificationManager.shared
    @StateObject private var updateManager = UpdateManager.shared
    @StateObject private var shortcutsManager = ShortcutsManager.shared
    @StateObject private var keyboardShortcutsManager = KeyboardShortcutsManager.shared
    
    @State private var showingSettings = false
    @State private var showingQuickSearch = false
    @State private var showingUpdate = false
    
    init() {
        // Configure app appearance
        setupAppearance()
        
        // Request notification permissions
        Task {
            await NotificationManager.shared.requestPermission()
        }
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .environmentObject(preferencesManager)
                .environmentObject(notificationManager)
                .environmentObject(updateManager)
                .environmentObject(shortcutsManager)
                .environmentObject(keyboardShortcutsManager)
                .onAppear {
                    setupApplication()
                }
                .onReceive(NotificationCenter.default.publisher(for: .showSettings)) { _ in
                    showingSettings = true
                }
                .onReceive(NotificationCenter.default.publisher(for: .showQuickSearch)) { _ in
                    showingQuickSearch = true
                }
                .onReceive(NotificationCenter.default.publisher(for: .showUpdate)) { _ in
                    showingUpdate = true
                }
                .sheet(isPresented: $showingSettings) {
                    SettingsView()
                        .environmentObject(preferencesManager)
                        .environmentObject(notificationManager)
                        .environmentObject(updateManager)
                        .environmentObject(keyboardShortcutsManager)
                }
                .sheet(isPresented: $showingQuickSearch) {
                    QuickSearchView()
                        .environmentObject(appState)
                }
                .sheet(isPresented: $showingUpdate) {
                    UpdateView()
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowToolbarStyle(.unified)
        .commands {
            AppCommands()
        }
        
        // Menu Bar Extra
        MenuBarExtra("WOW Monitor", systemImage: "server.rack") {
            MenuBarView()
                .environmentObject(appState)
                .environmentObject(preferencesManager)
        }
        .menuBarExtraStyle(.window)
        
        // Settings Window
        Settings {
            SettingsView()
                .environmentObject(preferencesManager)
                .environmentObject(notificationManager)
                .environmentObject(updateManager)
                .environmentObject(keyboardShortcutsManager)
        }
    }
    
    private func setupApplication() {
        // Initialize managers
        Task {
            await appState.initialize()
            await notificationManager.initialize()
            await shortcutsManager.initialize()
            keyboardShortcutsManager.initialize()
            
            // Check for updates on launch if enabled
            if updateManager.settings.automaticChecks {
                await updateManager.checkForUpdates()
            }
        }
        
        // Apply preferences
        applyPreferences()
        
        // Setup notification observers
        setupNotificationObservers()
    }
    
    private func setupAppearance() {
        // Configure window appearance
        if let window = NSApplication.shared.windows.first {
            window.titlebarAppearsTransparent = true
            window.titleVisibility = .hidden
        }
    }
    
    private func applyPreferences() {
        let preferences = preferencesManager.preferences
        
        // Apply appearance
        if let colorScheme = preferences.appearance.colorScheme {
            NSApp.appearance = NSAppearance(named: colorScheme == .dark ? .darkAqua : .aqua)
        }
        
        // Configure launch at login
        if preferences.general.launchAtLogin {
            enableLaunchAtLogin()
        }
    }
    
    private func setupNotificationObservers() {
        // Listen for preference changes
        NotificationCenter.default.addObserver(
            forName: .preferencesChanged,
            object: nil,
            queue: .main
        ) { _ in
            applyPreferences()
        }
        
        // Listen for app state changes
        NotificationCenter.default.addObserver(
            forName: .connectionStatusChanged,
            object: nil,
            queue: .main
        ) { notification in
            if let isConnected = notification.userInfo?["isConnected"] as? Bool {
                if !isConnected {
                    notificationManager.sendSystemNotification(
                        title: "Connection Lost",
                        message: "Lost connection to WOW Monitor backend",
                        priority: .high
                    )
                }
            }
        }
    }
    
    private func enableLaunchAtLogin() {
        let bundleIdentifier = Bundle.main.bundleIdentifier!
        let launcherBundleIdentifier = "\(bundleIdentifier).Launcher"
        
        // This would typically use SMLoginItemSetEnabled or similar
        // For now, we'll use a simplified approach
        let script = """
            tell application "System Events"
                make login item at end with properties {path:"\(Bundle.main.bundlePath)", hidden:false}
            end tell
        """
        
        if let appleScript = NSAppleScript(source: script) {
            appleScript.executeAndReturnError(nil)
        }
    }
}

// MARK: - App Commands

struct AppCommands: Commands {
    var body: some Commands {
        CommandGroup(replacing: .appInfo) {
            Button("About WOW Monitor") {
                NSApplication.shared.orderFrontStandardAboutPanel(nil)
            }
        }
        
        CommandGroup(after: .appInfo) {
            Divider()
            
            Button("Check for Updates...") {
                NotificationCenter.default.post(name: .showUpdate, object: nil)
            }
            .keyboardShortcut("u", modifiers: [.command, .shift])
        }
        
        CommandGroup(replacing: .newItem) {
            Button("Quick Search") {
                NotificationCenter.default.post(name: .showQuickSearch, object: nil)
            }
            .keyboardShortcut("k", modifiers: [.command])
        }
        
        CommandGroup(after: .newItem) {
            Divider()
            
            Button("Refresh All") {
                NotificationCenter.default.post(name: .refreshAll, object: nil)
            }
            .keyboardShortcut("r", modifiers: [.command])
            
            Button("Connect to Server") {
                NotificationCenter.default.post(name: .connectToServer, object: nil)
            }
            .keyboardShortcut("c", modifiers: [.command, .shift])
        }
        
        CommandGroup(replacing: .help) {
            Button("WOW Monitor Help") {
                if let url = URL(string: "https://docs.wowmonitor.com") {
                    NSWorkspace.shared.open(url)
                }
            }
            
            Button("Report an Issue") {
                if let url = URL(string: "https://github.com/wowmonitor/issues") {
                    NSWorkspace.shared.open(url)
                }
            }
            
            Button("Feature Requests") {
                if let url = URL(string: "https://github.com/wowmonitor/discussions") {
                    NSWorkspace.shared.open(url)
                }
            }
        }
        
        CommandGroup(after: .toolbar) {
            Button("Show in Menu Bar") {
                // Toggle menu bar visibility
            }
            .keyboardShortcut("m", modifiers: [.command, .shift])
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let showSettings = Notification.Name("showSettings")
    static let showQuickSearch = Notification.Name("showQuickSearch")
    static let showUpdate = Notification.Name("showUpdate")
    static let refreshAll = Notification.Name("refreshAll")
    static let connectToServer = Notification.Name("connectToServer")
    static let preferencesChanged = Notification.Name("preferencesChanged")
    static let connectionStatusChanged = Notification.Name("connectionStatusChanged")
    
    // Navigation notifications
    static let navigateToServices = Notification.Name("navigateToServices")
    static let navigateToDocker = Notification.Name("navigateToDocker")
    static let navigateToLogs = Notification.Name("navigateToLogs")
    static let navigateToMetrics = Notification.Name("navigateToMetrics")
    static let navigateToNotifications = Notification.Name("navigateToNotifications")
    
    // Action notifications
    static let restartAllServices = Notification.Name("restartAllServices")
    static let stopAllServices = Notification.Name("stopAllServices")
    static let restartDockerContainers = Notification.Name("restartDockerContainers")
    static let clearAllLogs = Notification.Name("clearAllLogs")
    static let exportSystemReport = Notification.Name("exportSystemReport")
}

// MARK: - App Delegate

class AppDelegate: NSObject, NSApplicationDelegate {
    func applicationDidFinishLaunching(_ notification: Notification) {
        // Configure app behavior
        NSApp.setActivationPolicy(.regular)
        
        // Hide dock icon if running in menu bar mode
        if UserDefaults.standard.bool(forKey: "RunInMenuBarOnly") {
            NSApp.setActivationPolicy(.accessory)
        }
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // Don't quit when last window is closed if running in menu bar mode
        return !UserDefaults.standard.bool(forKey: "RunInMenuBarOnly")
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        // Clean up resources
        SocketManager.shared.disconnect()
        KeyboardShortcutsManager.shared.cleanup()
    }
    
    func application(_ application: NSApplication, open urls: [URL]) {
        // Handle URL schemes (e.g., wowmonitor://action)
        for url in urls {
            handleURLScheme(url)
        }
    }
    
    private func handleURLScheme(_ url: URL) {
        guard url.scheme == "wowmonitor" else { return }
        
        switch url.host {
        case "services":
            NotificationCenter.default.post(name: .navigateToServices, object: nil)
        case "docker":
            NotificationCenter.default.post(name: .navigateToDocker, object: nil)
        case "logs":
            NotificationCenter.default.post(name: .navigateToLogs, object: nil)
        case "metrics":
            NotificationCenter.default.post(name: .navigateToMetrics, object: nil)
        case "notifications":
            NotificationCenter.default.post(name: .navigateToNotifications, object: nil)
        case "settings":
            NotificationCenter.default.post(name: .showSettings, object: nil)
        case "search":
            NotificationCenter.default.post(name: .showQuickSearch, object: nil)
        default:
            break
        }
    }
}

// MARK: - Environment Keys

struct AppStateKey: EnvironmentKey {
    static let defaultValue = AppState()
}

// PreferencesManagerKey is defined in PreferencesManager.swift

struct NotificationManagerKey: EnvironmentKey {
    static let defaultValue = NotificationManager.shared
}

struct UpdateManagerKey: EnvironmentKey {
    static let defaultValue = UpdateManager.shared
}

struct ShortcutsManagerKey: EnvironmentKey {
    static let defaultValue = ShortcutsManager.shared
}

struct KeyboardShortcutsManagerKey: EnvironmentKey {
    static let defaultValue = KeyboardShortcutsManager.shared
}

extension EnvironmentValues {
    var appState: AppState {
        get { self[AppStateKey.self] }
        set { self[AppStateKey.self] = newValue }
    }
    
    var notificationManager: NotificationManager {
        get { self[NotificationManagerKey.self] }
        set { self[NotificationManagerKey.self] = newValue }
    }
    
    var updateManager: UpdateManager {
        get { self[UpdateManagerKey.self] }
        set { self[UpdateManagerKey.self] = newValue }
    }
    
    var shortcutsManager: ShortcutsManager {
        get { self[ShortcutsManagerKey.self] }
        set { self[ShortcutsManagerKey.self] = newValue }
    }
    
    var keyboardShortcutsManager: KeyboardShortcutsManager {
        get { self[KeyboardShortcutsManagerKey.self] }
        set { self[KeyboardShortcutsManagerKey.self] = newValue }
    }
}

// MARK: - Preview Support

#if DEBUG
struct AppPreview: View {
    var body: some View {
        ContentView()
            .environmentObject(AppState())
            .environmentObject(PreferencesManager.shared)
            .environmentObject(NotificationManager.shared)
            .environmentObject(UpdateManager.shared)
            .environmentObject(ShortcutsManager.shared)
            .environmentObject(KeyboardShortcutsManager.shared)
    }
}

#Preview {
    AppPreview()
}
#endif