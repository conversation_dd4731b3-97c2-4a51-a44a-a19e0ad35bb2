{"": {"swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/APIClient.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/APIClient.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/APIClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/APIClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/APIClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/App.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/App.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/App.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/App~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/App.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/AppState.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/AppState.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/AppState.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/AppState~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/AppState.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ContentView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ContentView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ContentView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ContentView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ContentView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/DashboardView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DashboardView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DashboardView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DashboardView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DashboardView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/DockerView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DockerView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DockerView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DockerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/DockerView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ExportManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ExportManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ExportManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ExportManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ExportManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/KeyboardShortcutsManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/KeyboardShortcutsManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/KeyboardShortcutsManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/KeyboardShortcutsManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/KeyboardShortcutsManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/LogsView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/LogsView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/LogsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/LogsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/LogsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/MenuBarView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/MenuBarView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/MenuBarView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/MenuBarView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/MenuBarView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/Models.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/Models.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/Models.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/Models~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/Models.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/NotificationManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/NotificationsView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationsView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/NotificationsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/PreferencesManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/PreferencesManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/PreferencesManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/PreferencesManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/PreferencesManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/QuickSearchView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/QuickSearchView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/QuickSearchView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/QuickSearchView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/QuickSearchView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ServicesView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ServicesView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ServicesView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ServicesView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ServicesView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SettingsView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SettingsView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SettingsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SettingsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SettingsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ShortcutsManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ShortcutsManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ShortcutsManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ShortcutsManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/ShortcutsManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SocketManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SocketManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SocketManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SocketManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SocketManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SystemMetricsView.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SystemMetricsView.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SystemMetricsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SystemMetricsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/SystemMetricsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/UpdateManager.swift": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/UpdateManager.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/UpdateManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/UpdateManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/WOWMonitor.build/UpdateManager.swiftdeps"}}