/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/InternalCollectionsUtilities.build/Debugging.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
