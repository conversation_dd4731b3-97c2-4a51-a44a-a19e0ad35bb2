/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Ack/SocketAckEmitter.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Ack/SocketAckManager.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketAnyEvent.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketEventHandler.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClient.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientConfiguration.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientOption.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientSpec.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOStatus.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketRawView.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngine.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineClient.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEnginePacketType.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEnginePollable.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineSpec.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineWebsocket.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Manager/SocketManager.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Manager/SocketManagerSpec.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Parse/SocketPacket.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Parse/SocketParsable.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketExtensions.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketLogger.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketStringReader.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketTypes.swift
