/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/RequestCompression.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartFormData.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartUpload.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AlamofireExtended.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Protected.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPMethod.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/URLConvertible+URLRequestConvertible.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Combine.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/DispatchQueue+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/OperationQueue+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/StringEncoding+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLSessionConfiguration+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/Result+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLRequest+Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Alamofire.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Response.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/SessionDelegate.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoding.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Session.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestCompression.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Validation.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ServerTrustEvaluation.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ResponseSerialization.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/RequestTaskMap.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/URLEncodedFormEncoder.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoder.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/NetworkReachabilityManager.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/CachedResponseHandler.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RedirectHandler.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/AFError.swift /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/debug/Alamofire.build/DerivedSources/resource_bundle_accessor.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/EventMonitor.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AuthenticationInterceptor.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestInterceptor.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Notifications.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPHeaders.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Request.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataRequest.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DownloadRequest.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/UploadRequest.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataStreamRequest.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/WebSocketRequest.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RetryPolicy.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Concurrency.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/XPC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/unistd.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/sys_time.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Combine.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Dispatch.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_math.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_signal.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/System.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Darwin.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Foundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Observation.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_stdio.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_errno.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/IOKit.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
